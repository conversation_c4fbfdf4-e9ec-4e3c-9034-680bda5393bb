{"name": "v8n", "version": "1.5.1", "description": "Dead simple fluent JavaScript validation library", "main": "dist/v8n.cjs.js", "module": "dist/v8n.esm.js", "unpkg": "dist/v8n.min.js", "jsdelivr": "dist/v8n.min.js", "typings": "types/index.d.ts", "files": ["src", "dist/*.js", "types/*.d.ts"], "scripts": {"lint": "eslint ./src", "docs:dev": "vuepress dev docs", "docs:build": "vuepress build docs", "build": "rimraf -r dist && rollup -c", "deploy": "yarn build && npm publish", "format": "prettier --write ./src/**/*.js", "test": "yarn test:unit && yarn test:types", "test:unit": "jest", "test:types": "tsc -p ./types/test/tsconfig.json"}, "keywords": ["javascript", "validation", "library"], "author": "<PERSON> <<EMAIL>>", "repository": {"type": "git", "url": "git+https://github.com/imbrn/v8n.git"}, "bugs": {"url": "https://github.com/imbrn/v8n/issues"}, "homepage": "https://github.com/imbrn/v8n#readme", "license": "MIT", "devDependencies": {"@babel/core": "^7.12.3", "@babel/preset-env": "^7.12.1", "@rollup/plugin-buble": "^0.21.3", "@types/jest": "^26.0.15", "@types/node": "^14.14.7", "babel-jest": "^26.6.3", "eslint": "^7.13.0", "eslint-config-prettier": "^6.15.0", "eslint-plugin-jest": "^24.1.3", "husky": ">=4", "jest": "^26.6.3", "lint-staged": ">=10", "prettier": "^1.19.1", "rimraf": "^2.7.1", "rollup": "^2.33.1", "rollup-plugin-terser": "^7.0.2", "typescript": "^4.0.5", "vuepress": "^1.7.1", "vuepress-theme-carbon": "^1.0.0"}, "husky": {"hooks": {"pre-commit": "yarn test:types && lint-staged"}}, "lint-staged": {"*.js": ["eslint --cache --fix", "jest --bail --findRelatedTests"], "*.{js,css,md}": "prettier --write"}}