# Terminal Portfolio Website

A professional, interactive terminal-style portfolio website built with React, TypeScript, and Styled-Components. Features a 3D profile card and a fully functional terminal interface with custom commands.

## 🚀 Features

### Terminal Interface
- **Interactive Terminal** - Fully functional terminal with command history, autocomplete, and keyboard shortcuts
- **Custom Commands** - Portfolio-specific commands like `about`, `projects`, `skills`, `contact`, etc.
- **Basic Terminal Commands** - Standard commands like `ls`, `pwd`, `whoami`, `echo`, `clear`, `date`
- **Multiple Themes** - 5 different terminal themes (dark, matrix, cyberpunk, ubuntu, dracula)
- **Command History** - Navigate through previous commands with ↑/↓ arrows
- **Autocomplete** - TAB completion for commands
- **Color Coding** - Syntax highlighting for different command types

### 3D Profile Card
- **Interactive 3D Card** - Clickable card that flips to show contact information
- **Professional Design** - Clean, modern design with animations
- **Responsive Layout** - Works on desktop and mobile devices

### Professional Features
- **Resume Download** - Direct PDF download via terminal command
- **Project Showcase** - Detailed project listings with technologies and links
- **Skills Display** - Organized technical skills by category
- **Work Experience** - Professional experience timeline
- **Contact Information** - Multiple contact methods and social links

## 🛠️ Tech Stack

- **Frontend**: React 18, TypeScript
- **Styling**: Styled-Components
- **Animations**: Framer Motion
- **Build Tool**: Vite
- **Development**: Hot reload, TypeScript checking

## 📦 Installation

1. **Clone the repository**
   ```bash
   git clone <your-repo-url>
   cd terminal-portfolio
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Start development server**
   ```bash
   npm run dev
   ```

4. **Open in browser**
   ```
   http://localhost:3000
   ```

## 🎨 Customization

### Personal Information
Edit `src/data/portfolio.ts` to customize:
- Personal details (name, title, bio, location)
- Skills and technologies
- Projects and work experience
- Contact information
- Education background

### Terminal Commands
Add or modify commands in `src/utils/commands.ts`:
```typescript
export const commands: Record<string, Command> = {
  yourcommand: {
    name: 'yourcommand',
    description: 'Your command description',
    execute: (args: string[]) => {
      return createOutput('Your command output', 'success');
    }
  }
};
```

### Themes
Customize terminal themes in `src/styles/themes.ts` or add new ones:
```typescript
export const themes: Record<string, TerminalTheme> = {
  yourtheme: {
    name: 'Your Theme',
    background: '#your-bg-color',
    foreground: '#your-text-color',
    // ... other colors
  }
};
```

### Resume
Replace `public/resume.pdf` with your actual resume file.

## 🎯 Available Commands

| Command | Description |
|---------|-------------|
| `help` | Show all available commands |
| `about` | Display personal information |
| `projects` | Show portfolio projects |
| `skills` | Display technical skills |
| `experience` | Show work experience |
| `education` | Display educational background |
| `contact` | Get contact information |
| `resume` | Download resume PDF |
| `github` | Open GitHub profile |
| `clear` | Clear terminal screen |
| `history` | Show command history |
| `themes` | List available themes |
| `theme [name]` | Change terminal theme |
| `echo [text]` | Display text |
| `ls` | List directory contents |
| `pwd` | Print working directory |
| `whoami` | Display current user |
| `date` | Show current date/time |

## 🎨 Terminal Themes

- **dark** - Default dark theme
- **matrix** - Green matrix style
- **cyberpunk** - Neon cyberpunk theme
- **ubuntu** - Ubuntu terminal theme
- **dracula** - Dracula color scheme

## 🚀 Deployment

### Build for production
```bash
npm run build
```

### Deploy to Netlify, Vercel, or GitHub Pages
The built files will be in the `dist` folder, ready for deployment to any static hosting service.

## 📱 Responsive Design

- **Desktop**: Full layout with profile card and terminal side by side
- **Mobile**: Terminal-only view for optimal mobile experience

## 🔧 Development

### Project Structure
```
src/
├── components/          # React components
│   ├── Profile/        # 3D profile card
│   └── Terminal/       # Terminal interface
├── data/               # Portfolio data
├── hooks/              # Custom React hooks
├── styles/             # Styled-components and themes
├── types/              # TypeScript type definitions
└── utils/              # Utility functions and commands
```

### Adding New Features
1. **New Commands**: Add to `src/utils/commands.ts`
2. **New Themes**: Add to `src/styles/themes.ts`
3. **New Data**: Update `src/data/portfolio.ts`
4. **New Components**: Create in appropriate `src/components/` subfolder

## 📄 License

MIT License - feel free to use this project for your own portfolio!

## 🤝 Contributing

Contributions, issues, and feature requests are welcome!

---

**Made with ❤️ and lots of ☕**
