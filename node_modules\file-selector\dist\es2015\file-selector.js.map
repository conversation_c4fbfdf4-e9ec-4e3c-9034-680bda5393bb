{"version": 3, "file": "file-selector.js", "sourceRoot": "", "sources": ["../../src/file-selector.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAe,cAAc,EAAC,MAAM,QAAQ,CAAC;AAGpD,MAAM,eAAe,GAAG;IACpB,8CAA8C;IAC9C,WAAW;IACX,WAAW,CAAE,UAAU;CAC1B,CAAC;AAGF;;;;;;;;;GASG;AACH,MAAM,UAAgB,SAAS,CAAC,GAAgB;;QAC5C,IAAI,QAAQ,CAAY,GAAG,CAAC,IAAI,cAAc,CAAC,GAAG,CAAC,EAAE;YACjD,OAAO,oBAAoB,CAAC,GAAG,CAAC,YAAY,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC;SAC3D;aAAM,IAAI,WAAW,CAAC,GAAG,CAAC,EAAE;YACzB,OAAO,aAAa,CAAC,GAAG,CAAC,CAAC;SAC7B;aAAM,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,SAAS,IAAI,IAAI,IAAI,OAAO,IAAI,CAAC,OAAO,KAAK,UAAU,CAAC,EAAE;YACzG,OAAO,gBAAgB,CAAC,GAAG,CAAC,CAAA;SAC/B;QACD,OAAO,EAAE,CAAC;IACd,CAAC;CAAA;AAED,SAAS,cAAc,CAAC,KAAU;IAC9B,OAAO,QAAQ,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;AACxC,CAAC;AAED,SAAS,WAAW,CAAC,KAAU;IAC3B,OAAO,QAAQ,CAAQ,KAAK,CAAC,IAAI,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;AAC5D,CAAC;AAED,SAAS,QAAQ,CAAI,CAAM;IACvB,OAAO,OAAO,CAAC,KAAK,QAAQ,IAAI,CAAC,KAAK,IAAI,CAAA;AAC9C,CAAC;AAED,SAAS,aAAa,CAAC,GAAU;IAC7B,OAAO,QAAQ,CAAgB,GAAG,CAAC,MAA2B,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC;AAC5G,CAAC;AAED,oGAAoG;AACpG,SAAe,gBAAgB,CAAC,OAAc;;QAC1C,MAAM,KAAK,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;QAC/D,OAAO,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC;IACnD,CAAC;CAAA;AAGD,SAAe,oBAAoB,CAAC,EAAuB,EAAE,IAAY;;QACrE,IAAI,EAAE,KAAK,IAAI,EAAE;YACb,OAAO,EAAE,CAAC;SACb;QAED,2CAA2C;QAC3C,gGAAgG;QAChG,IAAI,EAAE,CAAC,KAAK,EAAE;YACV,MAAM,KAAK,GAAG,QAAQ,CAAmB,EAAE,CAAC,KAAK,CAAC;iBAC7C,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,KAAK,MAAM,CAAC,CAAC;YAC1C,0EAA0E;YAC1E,mEAAmE;YACnE,IAAI,IAAI,KAAK,MAAM,EAAE;gBACjB,OAAO,KAAK,CAAC;aAChB;YACD,MAAM,KAAK,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC,CAAC;YAC3D,OAAO,cAAc,CAAC,OAAO,CAAe,KAAK,CAAC,CAAC,CAAC;SACvD;QAED,OAAO,cAAc,CAAC,QAAQ,CAAe,EAAE,CAAC,KAAK,CAAC;aACjD,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAC5C,CAAC;CAAA;AAED,SAAS,cAAc,CAAC,KAAqB;IACzC,OAAO,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,eAAe,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AAC3E,CAAC;AAED,qCAAqC;AACrC,oHAAoH;AACpH,4DAA4D;AAC5D,wEAAwE;AACxE,SAAS,QAAQ,CAAI,KAA6C;IAC9D,IAAI,KAAK,KAAK,IAAI,EAAE;QAChB,OAAO,EAAE,CAAC;KACb;IAED,MAAM,KAAK,GAAG,EAAE,CAAC;IAEjB,gCAAgC;IAChC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACnC,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;QACtB,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;KACpB;IAED,OAAO,KAAY,CAAC;AACxB,CAAC;AAED,oEAAoE;AACpE,SAAS,cAAc,CAAC,IAAsB;IAC1C,IAAI,OAAO,IAAI,CAAC,gBAAgB,KAAK,UAAU,EAAE;QAC7C,OAAO,oBAAoB,CAAC,IAAI,CAAC,CAAC;KACrC;IAED,MAAM,KAAK,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;IAEtC,4FAA4F;IAC5F,uCAAuC;IACvC,gEAAgE;IAChE,IAAI,KAAK,IAAI,KAAK,CAAC,WAAW,EAAE;QAC5B,OAAO,YAAY,CAAC,KAAK,CAAQ,CAAC;KACrC;IAED,OAAO,oBAAoB,CAAC,IAAI,CAAC,CAAC;AACtC,CAAC;AAED,SAAS,OAAO,CAAI,KAAY;IAC5B,OAAO,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC;QAChC,GAAG,GAAG;QACN,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;KACvD,EAAE,EAAE,CAAC,CAAC;AACX,CAAC;AAED,SAAS,oBAAoB,CAAC,IAAsB;IAChD,MAAM,IAAI,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;IAC9B,IAAI,CAAC,IAAI,EAAE;QACP,OAAO,OAAO,CAAC,MAAM,CAAC,GAAG,IAAI,gBAAgB,CAAC,CAAC;KAClD;IACD,MAAM,GAAG,GAAG,cAAc,CAAC,IAAI,CAAC,CAAC;IACjC,OAAO,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;AAChC,CAAC;AAED,mEAAmE;AACnE,SAAe,SAAS,CAAC,KAAU;;QAC/B,OAAO,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC1E,CAAC;CAAA;AAED,4EAA4E;AAC5E,SAAS,YAAY,CAAC,KAAU;IAC5B,MAAM,MAAM,GAAG,KAAK,CAAC,YAAY,EAAE,CAAC;IAEpC,OAAO,IAAI,OAAO,CAAc,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;QAChD,MAAM,OAAO,GAA2B,EAAE,CAAC;QAE3C,SAAS,WAAW;YAChB,yFAAyF;YACzF,yFAAyF;YACzF,MAAM,CAAC,WAAW,CAAC,CAAO,KAAY,EAAE,EAAE;gBACtC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;oBACf,yBAAyB;oBACzB,IAAI;wBACA,MAAM,KAAK,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;wBACzC,OAAO,CAAC,KAAK,CAAC,CAAC;qBAClB;oBAAC,OAAO,GAAG,EAAE;wBACV,MAAM,CAAC,GAAG,CAAC,CAAC;qBACf;iBACJ;qBAAM;oBACH,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC;oBAChD,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;oBAEpB,mBAAmB;oBACnB,WAAW,EAAE,CAAC;iBACjB;YACL,CAAC,CAAA,EAAE,CAAC,GAAQ,EAAE,EAAE;gBACZ,MAAM,CAAC,GAAG,CAAC,CAAC;YAChB,CAAC,CAAC,CAAC;QACP,CAAC;QAED,WAAW,EAAE,CAAC;IAClB,CAAC,CAAC,CAAC;AACP,CAAC;AAED,uEAAuE;AACvE,SAAe,aAAa,CAAC,KAAU;;QACnC,OAAO,IAAI,OAAO,CAAe,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACjD,KAAK,CAAC,IAAI,CAAC,CAAC,IAAkB,EAAE,EAAE;gBAC9B,MAAM,GAAG,GAAG,cAAc,CAAC,IAAI,EAAE,KAAK,CAAC,QAAQ,CAAC,CAAC;gBACjD,OAAO,CAAC,GAAG,CAAC,CAAC;YACjB,CAAC,EAAE,CAAC,GAAQ,EAAE,EAAE;gBACZ,MAAM,CAAC,GAAG,CAAC,CAAC;YAChB,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;IACP,CAAC;CAAA"}