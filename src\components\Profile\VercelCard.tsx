import * as THREE from 'three'
import { useEffect, useRef, useState } from 'react'
import { Canvas, extend, useThree, use<PERSON>rame } from '@react-three/fiber'
import { Environment, Lightformer, Text, RenderTexture, PerspectiveCamera } from '@react-three/drei'
import { BallCollider, CuboidCollider, Physics, RigidBody, useRopeJoint, useSphericalJoint, RapierRigidBody } from '@react-three/rapier'
import { MeshLineGeometry, MeshLineMaterial } from 'meshline'
import styled from 'styled-components'
import { personalInfo } from '../../data/portfolio'

extend({ MeshLineGeometry, MeshLineMaterial })

const CanvasContainer = styled.div`
  width: 100%;
  height: 100%;
  position: relative;
  background: transparent;
`

// Badge texture component for rendering custom content
function BadgeTexture({ user = personalInfo }) {
  return (
    <>
      <PerspectiveCamera makeDefault manual aspect={1.05} position={[0.49, 0.22, 2]} />
      
      {/* Background */}
      <mesh>
        <planeGeometry args={[2, 2.5]} />
        <meshBasicMaterial color="#1a1a2e" />
      </mesh>
      
      {/* Profile Section */}
      <mesh position={[0, 0.8, 0.01]}>
        <circleGeometry args={[0.3]} />
        <meshBasicMaterial color="#00ffff" />
      </mesh>
      
      {/* Profile Emoji */}
      <Text
        position={[0, 0.8, 0.02]}
        fontSize={0.4}
        color="#ffffff"
        anchorX="center"
        anchorY="middle"
      >
        👨‍💻
      </Text>
      
      {/* Name */}
      <Text
        position={[0, 0.3, 0.01]}
        fontSize={0.12}
        color="#ffffff"
        anchorX="center"
        anchorY="middle"
        maxWidth={1.8}
      >
        {user.name}
      </Text>
      
      {/* Title */}
      <Text
        position={[0, 0.1, 0.01]}
        fontSize={0.08}
        color="#00ffff"
        anchorX="center"
        anchorY="middle"
        maxWidth={1.8}
      >
        {user.title}
      </Text>
      
      {/* Experience */}
      <Text
        position={[0, -0.2, 0.01]}
        fontSize={0.07}
        color="#ffff00"
        anchorX="center"
        anchorY="middle"
      >
        {user.yearsOfExperience}+ Years Experience
      </Text>
      
      {/* Location */}
      <Text
        position={[0, -0.4, 0.01]}
        fontSize={0.06}
        color="#ffffff"
        anchorX="center"
        anchorY="middle"
      >
        📍 {user.location}
      </Text>
    </>
  )
}

function Band({ maxSpeed = 50, minSpeed = 10 }) {
  const band = useRef<THREE.Mesh>(null)
  const fixed = useRef<RapierRigidBody>(null)
  const j1 = useRef<RapierRigidBody>(null)
  const j2 = useRef<RapierRigidBody>(null)
  const j3 = useRef<RapierRigidBody>(null)
  const card = useRef<RapierRigidBody>(null)

  const vec = new THREE.Vector3(), ang = new THREE.Vector3(), rot = new THREE.Vector3(), dir = new THREE.Vector3()
  const segmentProps = { type: 'dynamic' as const, canSleep: true, angularDamping: 2, linearDamping: 2 }

  const { width, height } = useThree((state) => state.size)
  const [curve] = useState(() => new THREE.CatmullRomCurve3([new THREE.Vector3(), new THREE.Vector3(), new THREE.Vector3(), new THREE.Vector3()]))
  const [dragged, drag] = useState<false | THREE.Vector3>(false)
  const [hovered, hover] = useState(false)

  useRopeJoint(fixed, j1, [[0, 0, 0], [0, 0, 0], 1])
  useRopeJoint(j1, j2, [[0, 0, 0], [0, 0, 0], 1])
  useRopeJoint(j2, j3, [[0, 0, 0], [0, 0, 0], 1])
  useSphericalJoint(j3, card, [[0, 0, 0], [0, 1.45, 0]])

  useEffect(() => {
    if (hovered) {
      document.body.style.cursor = dragged ? 'grabbing' : 'grab'
      return () => void (document.body.style.cursor = 'auto')
    }
  }, [hovered, dragged])

  useFrame((state, delta) => {
    if (dragged && card.current) {
      vec.set(state.pointer.x, state.pointer.y, 0.5).unproject(state.camera)
      dir.copy(vec).sub(state.camera.position).normalize()
      vec.add(dir.multiplyScalar(state.camera.position.length()))

      // Wake up all physics bodies
      ;[card, j1, j2, j3, fixed].forEach((ref) => ref.current?.wakeUp())

      // Set kinematic position
      card.current.setNextKinematicTranslation({
        x: vec.x - dragged.x,
        y: vec.y - dragged.y,
        z: vec.z - dragged.z
      })
    }

    if (fixed.current && j1.current && j2.current && j3.current && card.current && band.current) {
      // Fix most of the jitter when over pulling the card
      ;[j1, j2].forEach((ref) => {
        if (ref.current) {
          if (!(ref.current as any).lerped) {
            (ref.current as any).lerped = new THREE.Vector3().copy(ref.current.translation())
          }
          const clampedDistance = Math.max(0.1, Math.min(1, (ref.current as any).lerped.distanceTo(ref.current.translation())))
          ;(ref.current as any).lerped.lerp(ref.current.translation(), delta * (minSpeed + clampedDistance * (maxSpeed - minSpeed)))
        }
      })

      // Calculate catmul curve
      curve.points[0].copy(j3.current.translation())
      curve.points[1].copy((j2.current as any).lerped || j2.current.translation())
      curve.points[2].copy((j1.current as any).lerped || j1.current.translation())
      curve.points[3].copy(fixed.current.translation())

      // Update the band geometry
      if (band.current && band.current.geometry) {
        band.current.geometry.dispose()
        band.current.geometry = new THREE.TubeGeometry(curve, 32, 0.02, 8, false)
      }

      // Tilt it back towards the screen
      ang.copy(card.current.angvel())
      rot.copy(card.current.rotation())
      card.current.setAngvel({ x: ang.x, y: ang.y - rot.y * 0.25, z: ang.z }, true)
    }
  })

  curve.curveType = 'chordal'

  return (
    <>
      <group position={[0, 4, 0]}>
        <RigidBody ref={fixed} {...segmentProps} type="fixed" />
        <RigidBody position={[0.5, 0, 0]} ref={j1} {...segmentProps}>
          <BallCollider args={[0.1]} />
        </RigidBody>
        <RigidBody position={[1, 0, 0]} ref={j2} {...segmentProps}>
          <BallCollider args={[0.1]} />
        </RigidBody>
        <RigidBody position={[1.5, 0, 0]} ref={j3} {...segmentProps}>
          <BallCollider args={[0.1]} />
        </RigidBody>
        <RigidBody position={[2, 0, 0]} ref={card} {...segmentProps} type={dragged ? 'kinematicPosition' : 'dynamic'}>
          <CuboidCollider args={[0.8, 1.125, 0.01]} />
          <group
            scale={2.25}
            position={[0, -1.2, -0.05]}
            onPointerOver={() => hover(true)}
            onPointerOut={() => hover(false)}
            onPointerUp={(e: any) => {
              e.target?.releasePointerCapture?.(e.pointerId)
              drag(false)
            }}
            onPointerDown={(e: any) => {
              e.target?.setPointerCapture?.(e.pointerId)
              if (card.current) {
                const offset = new THREE.Vector3().copy(e.point).sub(vec.copy(card.current.translation()))
                drag(offset)
              }
            }}
          >
            {/* Main Card */}
            <mesh>
              <boxGeometry args={[1.6, 2.25, 0.1]} />
              <meshPhysicalMaterial 
                clearcoat={1} 
                clearcoatRoughness={0.15} 
                roughness={0.3} 
                metalness={0.5}
                color="#2a2a3e"
              >
                <RenderTexture attach="map" height={1024} width={1024}>
                  <BadgeTexture user={personalInfo} />
                </RenderTexture>
              </meshPhysicalMaterial>
            </mesh>
            
            {/* Card Border */}
            <mesh position={[0, 0, -0.02]}>
              <boxGeometry args={[1.65, 2.3, 0.08]} />
              <meshStandardMaterial
                color="#00ffff"
                emissive="#00ffff"
                emissiveIntensity={0.2}
                transparent
                opacity={0.3}
              />
            </mesh>
            
            {/* Clip/Holder */}
            <mesh position={[0, 1.1, 0.06]}>
              <boxGeometry args={[0.3, 0.1, 0.05]} />
              <meshStandardMaterial color="#666666" metalness={0.8} roughness={0.2} />
            </mesh>
          </group>
        </RigidBody>
      </group>
      
      {/* Lanyard - Simple tube for now */}
      <mesh ref={band}>
        <tubeGeometry args={[curve, 32, 0.02, 8, false]} />
        <meshStandardMaterial color="#666666" />
      </mesh>
    </>
  )
}

export default function VercelCardApp() {
  return (
    <CanvasContainer>
      <Canvas camera={{ position: [0, 0, 13], fov: 25 }}>
        <ambientLight intensity={Math.PI} />
        <Physics interpolate gravity={[0, -40, 0]} timeStep={1 / 60}>
          <Band />
        </Physics>
        <Environment background blur={0.75}>
          <color attach="background" args={['#0a0a0a']} />
          <Lightformer intensity={2} color="white" position={[0, -1, 5]} rotation={[0, 0, Math.PI / 3]} scale={[100, 0.1, 1]} />
          <Lightformer intensity={3} color="white" position={[-1, -1, 1]} rotation={[0, 0, Math.PI / 3]} scale={[100, 0.1, 1]} />
          <Lightformer intensity={3} color="white" position={[1, 1, 1]} rotation={[0, 0, Math.PI / 3]} scale={[100, 0.1, 1]} />
          <Lightformer intensity={10} color="white" position={[-10, 0, 14]} rotation={[0, Math.PI / 2, Math.PI / 3]} scale={[100, 10, 1]} />
        </Environment>
      </Canvas>
    </CanvasContainer>
  )
}
