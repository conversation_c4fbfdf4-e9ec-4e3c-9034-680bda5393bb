/// <reference types="react" />
declare type Props<V, Settings> = {
    type: string;
    value: V;
    settings?: Settings;
    setValue: (v: V) => void;
};
export declare function useInputSetters<V, Settings extends object>({ value, type, settings, setValue }: Props<V, Settings>): {
    displayValue: any;
    onChange: import("react").Dispatch<any>;
    onUpdate: (updatedValue: any) => void;
};
export {};
