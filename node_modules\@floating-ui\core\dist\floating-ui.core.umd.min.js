!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).FloatingUICore={})}(this,(function(t){"use strict";function e(t){return t.split("-")[0]}function n(t){return t.split("-")[1]}function i(t){return["top","bottom"].includes(e(t))?"x":"y"}function o(t){return"y"===t?"height":"width"}function r(t,r,a){let{reference:l,floating:s}=t;const f=l.x+l.width/2-s.width/2,c=l.y+l.height/2-s.height/2,u=i(r),m=o(u),d=l[m]/2-s[m]/2,g="x"===u;let p;switch(e(r)){case"top":p={x:f,y:l.y-s.height};break;case"bottom":p={x:f,y:l.y+l.height};break;case"right":p={x:l.x+l.width,y:c};break;case"left":p={x:l.x-s.width,y:c};break;default:p={x:l.x,y:l.y}}switch(n(r)){case"start":p[u]-=d*(a&&g?-1:1);break;case"end":p[u]+=d*(a&&g?-1:1)}return p}function a(t){return"number"!=typeof t?function(t){return{top:0,right:0,bottom:0,left:0,...t}}(t):{top:t,right:t,bottom:t,left:t}}function l(t){return{...t,top:t.y,left:t.x,right:t.x+t.width,bottom:t.y+t.height}}async function s(t,e){var n;void 0===e&&(e={});const{x:i,y:o,platform:r,rects:s,elements:f,strategy:c}=t,{boundary:u="clippingAncestors",rootBoundary:m="viewport",elementContext:d="floating",altBoundary:g=!1,padding:p=0}=e,h=a(p),y=f[g?"floating"===d?"reference":"floating":d],x=l(await r.getClippingRect({element:null==(n=await(null==r.isElement?void 0:r.isElement(y)))||n?y:y.contextElement||await(null==r.getDocumentElement?void 0:r.getDocumentElement(f.floating)),boundary:u,rootBoundary:m,strategy:c})),w=l(r.convertOffsetParentRelativeRectToViewportRelativeRect?await r.convertOffsetParentRelativeRectToViewportRelativeRect({rect:"floating"===d?{...s.floating,x:i,y:o}:s.reference,offsetParent:await(null==r.getOffsetParent?void 0:r.getOffsetParent(f.floating)),strategy:c}):s[d]);return{top:x.top-w.top+h.top,bottom:w.bottom-x.bottom+h.bottom,left:x.left-w.left+h.left,right:w.right-x.right+h.right}}const f=Math.min,c=Math.max;function u(t,e,n){return c(t,f(e,n))}const m={left:"right",right:"left",bottom:"top",top:"bottom"};function d(t){return t.replace(/left|right|bottom|top/g,(t=>m[t]))}function g(t,e,r){void 0===r&&(r=!1);const a=n(t),l=i(t),s=o(l);let f="x"===l?a===(r?"end":"start")?"right":"left":"start"===a?"bottom":"top";return e.reference[s]>e.floating[s]&&(f=d(f)),{main:f,cross:d(f)}}const p={start:"end",end:"start"};function h(t){return t.replace(/start|end/g,(t=>p[t]))}const y=["top","right","bottom","left"],x=y.reduce(((t,e)=>t.concat(e,e+"-start",e+"-end")),[]);function w(t,e){return{top:t.top-e.height,right:t.right-e.width,bottom:t.bottom-e.height,left:t.left-e.width}}function v(t){return y.some((e=>t[e]>=0))}function b(t){return"x"===t?"y":"x"}t.arrow=t=>({name:"arrow",options:t,async fn(e){const{element:r,padding:l=0}=null!=t?t:{},{x:s,y:f,placement:c,rects:m,platform:d}=e;if(null==r)return{};const g=a(l),p={x:s,y:f},h=i(c),y=n(c),x=o(h),w=await d.getDimensions(r),v="y"===h?"top":"left",b="y"===h?"bottom":"right",R=m.reference[x]+m.reference[h]-p[h]-m.floating[x],A=p[h]-m.reference[h],P=await(null==d.getOffsetParent?void 0:d.getOffsetParent(r));let T=P?"y"===h?P.clientHeight||0:P.clientWidth||0:0;0===T&&(T=m.floating[x]);const O=R/2-A/2,D=g[v],L=T-w[x]-g[b],k=T/2-w[x]/2+O,C=u(D,k,L),E=("start"===y?g[v]:g[b])>0&&k!==C&&m.reference[x]<=m.floating[x];return{[h]:p[h]-(E?k<D?D-k:L-k:0),data:{[h]:C,centerOffset:k-C}}}}),t.autoPlacement=function(t){return void 0===t&&(t={}),{name:"autoPlacement",options:t,async fn(i){var o,r,a,l,f;const{x:c,y:u,rects:m,middlewareData:d,placement:p,platform:y,elements:w}=i,{alignment:v=null,allowedPlacements:b=x,autoAlignment:R=!0,...A}=t,P=function(t,i,o){return(t?[...o.filter((e=>n(e)===t)),...o.filter((e=>n(e)!==t))]:o.filter((t=>e(t)===t))).filter((e=>!t||n(e)===t||!!i&&h(e)!==e))}(v,R,b),T=await s(i,A),O=null!=(o=null==(r=d.autoPlacement)?void 0:r.index)?o:0,D=P[O];if(null==D)return{};const{main:L,cross:k}=g(D,m,await(null==y.isRTL?void 0:y.isRTL(w.floating)));if(p!==D)return{x:c,y:u,reset:{placement:P[0]}};const C=[T[e(D)],T[L],T[k]],E=[...null!=(a=null==(l=d.autoPlacement)?void 0:l.overflows)?a:[],{placement:D,overflows:C}],H=P[O+1];if(H)return{data:{index:O+1,overflows:E},reset:{placement:H}};const B=E.slice().sort(((t,e)=>t.overflows[0]-e.overflows[0])),V=null==(f=B.find((t=>{let{overflows:e}=t;return e.every((t=>t<=0))})))?void 0:f.placement,j=null!=V?V:B[0].placement;return j!==p?{data:{index:O+1,overflows:E},reset:{placement:j}}:{}}}},t.computePosition=async(t,e,n)=>{const{placement:i="bottom",strategy:o="absolute",middleware:a=[],platform:l}=n,s=await(null==l.isRTL?void 0:l.isRTL(e));let f=await l.getElementRects({reference:t,floating:e,strategy:o}),{x:c,y:u}=r(f,i,s),m=i,d={},g=0;for(let n=0;n<a.length;n++){const{name:p,fn:h}=a[n],{x:y,y:x,data:w,reset:v}=await h({x:c,y:u,initialPlacement:i,placement:m,strategy:o,middlewareData:d,rects:f,platform:l,elements:{reference:t,floating:e}});c=null!=y?y:c,u=null!=x?x:u,d={...d,[p]:{...d[p],...w}},v&&g<=50&&(g++,"object"==typeof v&&(v.placement&&(m=v.placement),v.rects&&(f=!0===v.rects?await l.getElementRects({reference:t,floating:e,strategy:o}):v.rects),({x:c,y:u}=r(f,m,s))),n=-1)}return{x:c,y:u,placement:m,strategy:o,middlewareData:d}},t.detectOverflow=s,t.flip=function(t){return void 0===t&&(t={}),{name:"flip",options:t,async fn(n){var i;const{placement:o,middlewareData:r,rects:a,initialPlacement:l,platform:f,elements:c}=n,{mainAxis:u=!0,crossAxis:m=!0,fallbackPlacements:p,fallbackStrategy:y="bestFit",flipAlignment:x=!0,...w}=t,v=e(o),b=p||(v===l||!x?[d(l)]:function(t){const e=d(t);return[h(t),e,h(e)]}(l)),R=[l,...b],A=await s(n,w),P=[];let T=(null==(i=r.flip)?void 0:i.overflows)||[];if(u&&P.push(A[v]),m){const{main:t,cross:e}=g(o,a,await(null==f.isRTL?void 0:f.isRTL(c.floating)));P.push(A[t],A[e])}if(T=[...T,{placement:o,overflows:P}],!P.every((t=>t<=0))){var O,D;const t=(null!=(O=null==(D=r.flip)?void 0:D.index)?O:0)+1,e=R[t];if(e)return{data:{index:t,overflows:T},reset:{placement:e}};let n="bottom";switch(y){case"bestFit":{var L;const t=null==(L=T.map((t=>[t,t.overflows.filter((t=>t>0)).reduce(((t,e)=>t+e),0)])).sort(((t,e)=>t[1]-e[1]))[0])?void 0:L[0].placement;t&&(n=t);break}case"initialPlacement":n=l}if(o!==n)return{reset:{placement:n}}}return{}}}},t.hide=function(t){let{strategy:e="referenceHidden",...n}=void 0===t?{}:t;return{name:"hide",async fn(t){const{rects:i}=t;switch(e){case"referenceHidden":{const e=w(await s(t,{...n,elementContext:"reference"}),i.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:v(e)}}}case"escaped":{const e=w(await s(t,{...n,altBoundary:!0}),i.floating);return{data:{escapedOffsets:e,escaped:v(e)}}}default:return{}}}}},t.inline=function(t){return void 0===t&&(t={}),{name:"inline",options:t,async fn(n){var o;const{placement:r,elements:s,rects:u,platform:m,strategy:d}=n,{padding:g=2,x:p,y:h}=t,y=l(m.convertOffsetParentRelativeRectToViewportRelativeRect?await m.convertOffsetParentRelativeRectToViewportRelativeRect({rect:u.reference,offsetParent:await(null==m.getOffsetParent?void 0:m.getOffsetParent(s.floating)),strategy:d}):u.reference),x=null!=(o=await(null==m.getClientRects?void 0:m.getClientRects(s.reference)))?o:[],w=a(g);const v=await m.getElementRects({reference:{getBoundingClientRect:function(){var t;if(2===x.length&&x[0].left>x[1].right&&null!=p&&null!=h)return null!=(t=x.find((t=>p>t.left-w.left&&p<t.right+w.right&&h>t.top-w.top&&h<t.bottom+w.bottom)))?t:y;if(x.length>=2){if("x"===i(r)){const t=x[0],n=x[x.length-1],i="top"===e(r),o=t.top,a=n.bottom,l=i?t.left:n.left,s=i?t.right:n.right;return{top:o,bottom:a,left:l,right:s,width:s-l,height:a-o,x:l,y:o}}const t="left"===e(r),n=c(...x.map((t=>t.right))),o=f(...x.map((t=>t.left))),a=x.filter((e=>t?e.left===o:e.right===n)),l=a[0].top,s=a[a.length-1].bottom;return{top:l,bottom:s,left:o,right:n,width:n-o,height:s-l,x:o,y:l}}return y}},floating:s.floating,strategy:d});return u.reference.x!==v.reference.x||u.reference.y!==v.reference.y||u.reference.width!==v.reference.width||u.reference.height!==v.reference.height?{reset:{rects:v}}:{}}}},t.limitShift=function(t){return void 0===t&&(t={}),{options:t,fn(n){const{x:o,y:r,placement:a,rects:l,middlewareData:s}=n,{offset:f=0,mainAxis:c=!0,crossAxis:u=!0}=t,m={x:o,y:r},d=i(a),g=b(d);let p=m[d],h=m[g];const y="function"==typeof f?f({...l,placement:a}):f,x="number"==typeof y?{mainAxis:y,crossAxis:0}:{mainAxis:0,crossAxis:0,...y};if(c){const t="y"===d?"height":"width",e=l.reference[d]-l.floating[t]+x.mainAxis,n=l.reference[d]+l.reference[t]-x.mainAxis;p<e?p=e:p>n&&(p=n)}if(u){var w,v,R,A;const t="y"===d?"width":"height",n=["top","left"].includes(e(a)),i=l.reference[g]-l.floating[t]+(n&&null!=(w=null==(v=s.offset)?void 0:v[g])?w:0)+(n?0:x.crossAxis),o=l.reference[g]+l.reference[t]+(n?0:null!=(R=null==(A=s.offset)?void 0:A[g])?R:0)-(n?x.crossAxis:0);h<i?h=i:h>o&&(h=o)}return{[d]:p,[g]:h}}}},t.offset=function(t){return void 0===t&&(t=0),{name:"offset",options:t,async fn(o){const{x:r,y:a}=o,l=await async function(t,o){const{placement:r,platform:a,elements:l}=t,s=await(null==a.isRTL?void 0:a.isRTL(l.floating)),f=e(r),c=n(r),u="x"===i(r),m=["left","top"].includes(f)?-1:1,d=s&&u?-1:1,g="function"==typeof o?o(t):o;let{mainAxis:p,crossAxis:h,alignmentAxis:y}="number"==typeof g?{mainAxis:g,crossAxis:0,alignmentAxis:null}:{mainAxis:0,crossAxis:0,alignmentAxis:null,...g};return c&&"number"==typeof y&&(h="end"===c?-1*y:y),u?{x:h*d,y:p*m}:{x:p*m,y:h*d}}(o,t);return{x:r+l.x,y:a+l.y,data:l}}}},t.rectToClientRect=l,t.shift=function(t){return void 0===t&&(t={}),{name:"shift",options:t,async fn(n){const{x:o,y:r,placement:a}=n,{mainAxis:l=!0,crossAxis:f=!1,limiter:c={fn:t=>{let{x:e,y:n}=t;return{x:e,y:n}}},...m}=t,d={x:o,y:r},g=await s(n,m),p=i(e(a)),h=b(p);let y=d[p],x=d[h];if(l){const t="y"===p?"bottom":"right";y=u(y+g["y"===p?"top":"left"],y,y-g[t])}if(f){const t="y"===h?"bottom":"right";x=u(x+g["y"===h?"top":"left"],x,x-g[t])}const w=c.fn({...n,[p]:y,[h]:x});return{...w,data:{x:w.x-o,y:w.y-r}}}}},t.size=function(t){return void 0===t&&(t={}),{name:"size",options:t,async fn(i){const{placement:o,rects:r,platform:a,elements:l}=i,{apply:f,...u}=t,m=await s(i,u),d=e(o),g=n(o);let p,h;"top"===d||"bottom"===d?(p=d,h=g===(await(null==a.isRTL?void 0:a.isRTL(l.floating))?"start":"end")?"left":"right"):(h=d,p="end"===g?"top":"bottom");const y=c(m.left,0),x=c(m.right,0),w=c(m.top,0),v=c(m.bottom,0),b={availableHeight:r.floating.height-(["left","right"].includes(o)?2*(0!==w||0!==v?w+v:c(m.top,m.bottom)):m[p]),availableWidth:r.floating.width-(["top","bottom"].includes(o)?2*(0!==y||0!==x?y+x:c(m.left,m.right)):m[h])},R=await a.getDimensions(l.floating);null==f||f({...i,...b});const A=await a.getDimensions(l.floating);return R.width!==A.width||R.height!==A.height?{reset:{rects:!0}}:{}}}},Object.defineProperty(t,"__esModule",{value:!0})}));
