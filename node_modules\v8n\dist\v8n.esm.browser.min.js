class e{constructor(e,t,n,s){this.name=e,this.fn=t,this.args=n,this.modifiers=s}_test(e){let t=this.fn;try{n(this.modifiers.slice(),t,this)(e)}catch(e){t=()=>!1}try{return n(this.modifiers.slice(),t,this)(e)}catch(e){return!1}}_check(e){try{n(this.modifiers.slice(),this.fn,this)(e)}catch(e){if(n(this.modifiers.slice(),(e=>e),this)(!1))return}if(!n(this.modifiers.slice(),this.fn,this)(e))throw null}_testAsync(e){return new Promise(((t,n)=>{s(this.modifiers.slice(),this.fn,this)(e).then((s=>{s?t(e):n(null)})).catch((e=>n(e)))}))}}function t(e,t="simple"){return"object"==typeof e?e[t]:e}function n(e,s,r){if(e.length){const t=e.shift(),i=n(e,s,r);return t.perform(i,r)}return t(s)}function s(e,n,r){if(e.length){const t=e.shift(),i=s(e,n,r);return t.performAsync(i,r)}return e=>Promise.resolve(t(n,"async")(e))}class r{constructor(e,t,n){this.name=e,this.perform=t,this.performAsync=n}}class i extends Error{constructor(e,t,n,s,...r){super(r),Error.captureStackTrace&&Error.captureStackTrace(this,i),this.rule=e,this.value=t,this.cause=n,this.target=s}}class c{constructor(e=[],t=[]){this.chain=e,this.nextRuleModifiers=t}_applyRule(t,n){return(...s)=>(this.chain.push(new e(n,t.apply(this,s),s,this.nextRuleModifiers)),this.nextRuleModifiers=[],this)}_applyModifier(e,t){return this.nextRuleModifiers.push(new r(t,e.simple,e.async)),this}_clone(){return new c(this.chain.slice(),this.nextRuleModifiers.slice())}test(e){return this.chain.every((t=>t._test(e)))}testAll(e){const t=[];return this.chain.forEach((n=>{try{n._check(e)}catch(s){t.push(new i(n,e,s))}})),t}check(e){this.chain.forEach((t=>{try{t._check(e)}catch(n){throw new i(t,e,n)}}))}testAsync(e){return new Promise(((t,n)=>{o(e,this.chain.slice(),t,n)}))}}function o(e,t,n,s){if(t.length){const r=t.shift();r._testAsync(e).then((()=>{o(e,t,n,s)}),(t=>{s(new i(r,e,t))}))}else n(e)}const h=(e,t)=>!(!t||"string"!=typeof e||0!==e.trim().length)||null==e;function a(){return"undefined"!=typeof Proxy?u(new c):f(new c)}let l={};function u(e){return new Proxy(e,{get(t,n){if(n in t)return t[n];const s=u(e._clone());return n in y?s._applyModifier(y[n],n):n in l?s._applyRule(l[n],n):n in g?s._applyRule(g[n],n):void 0}})}function f(e){const t=(e,t)=>(Object.keys(e).forEach((n=>{t[n]=(...s)=>f(t._clone())._applyRule(e[n],n)(...s)})),t),n=t(g,e),s=t(l,n);return Object.keys(y).forEach((e=>{Object.defineProperty(s,e,{get:()=>f(s._clone())._applyModifier(y[e],e)})})),s}a.extend=function(e){Object.assign(l,e)},a.clearCustomRules=function(){l={}};const y={not:{simple:e=>t=>!e(t),async:e=>t=>Promise.resolve(e(t)).then((e=>!e)).catch((()=>!0))},some:{simple:e=>t=>m(t).some((t=>{try{return e(t)}catch(e){return!1}})),async:e=>t=>Promise.all(m(t).map((t=>{try{return e(t).catch((()=>!1))}catch(e){return!1}}))).then((e=>e.some(Boolean)))},every:{simple:e=>t=>!1!==t&&m(t).every(e),async:e=>t=>Promise.all(m(t).map(e)).then((e=>e.every(Boolean)))},strict:{simple:(e,t)=>n=>p(t)&&n&&"object"==typeof n?Object.keys(t.args[0]).length===Object.keys(n).length&&e(n):e(n),async:(e,t)=>n=>Promise.resolve(e(n)).then((e=>p(t)&&n&&"object"==typeof n?Object.keys(t.args[0]).length===Object.keys(n).length&&e:e)).catch((()=>!1))}};function p(e){return e&&"schema"===e.name&&e.args.length>0&&"object"==typeof e.args[0]}function m(e){return"string"==typeof e?e.split(""):e}const g={equal:e=>t=>t==e,exact:e=>t=>t===e,number:(e=!0)=>t=>"number"==typeof t&&(e||isFinite(t)),integer:()=>e=>(Number.isInteger||b)(e),numeric:()=>e=>!isNaN(parseFloat(e))&&isFinite(e),string:()=>d("string"),boolean:()=>d("boolean"),undefined:()=>d("undefined"),null:()=>d("null"),array:()=>d("array"),object:()=>d("object"),instanceOf:e=>t=>t instanceof e,pattern:e=>t=>e.test(t),lowercase:()=>e=>"boolean"==typeof e||e===e.toLowerCase()&&""!==e.trim(),uppercase:()=>e=>e===e.toUpperCase()&&""!==e.trim(),vowel:()=>e=>/^[aeiou]+$/i.test(e),consonant:()=>e=>/^(?=[^aeiou])([a-z]+)$/i.test(e),first:e=>t=>t[0]==e,last:e=>t=>t[t.length-1]==e,empty:()=>e=>0===e.length,length:(e,t)=>n=>n.length>=e&&n.length<=(t||e),minLength:e=>t=>t.length>=e,maxLength:e=>t=>t.length<=e,negative:()=>e=>e<0,positive:()=>e=>e>=0,between:(e,t)=>n=>n>=e&&n<=t,range:(e,t)=>n=>n>=e&&n<=t,lessThan:e=>t=>t<e,lessThanOrEqual:e=>t=>t<=e,greaterThan:e=>t=>t>e,greaterThanOrEqual:e=>t=>t>=e,even:()=>e=>e%2==0,odd:()=>e=>e%2!=0,includes:e=>t=>~t.indexOf(e),schema:e=>function(e){return{simple:t=>{const n=[];if(Object.keys(e).forEach((s=>{const r=e[s];try{r.check((t||{})[s])}catch(e){e.target=s,n.push(e)}})),n.length>0)throw n;return!0},async:t=>{const n=[],s=Object.keys(e).map((s=>e[s].testAsync((t||{})[s]).catch((e=>{e.target=s,n.push(e)}))));return Promise.all(s).then((()=>{if(n.length>0)throw n;return!0}))}}}(e),passesAnyOf:(...e)=>t=>e.some((e=>e.test(t))),optional:(e,t=!1)=>({simple:n=>h(n,t)||void 0===e.check(n),async:n=>h(n,t)||e.testAsync(n)})};function d(e){return t=>Array.isArray(t)&&"array"===e||null===t&&"null"===e||typeof t===e}function b(e){return"number"==typeof e&&isFinite(e)&&Math.floor(e)===e}export default a;
//# sourceMappingURL=v8n.esm.browser.min.js.map
