/* tslint:disable */
/* eslint-disable */
export const memory: WebAssembly.Memory;
export const version: (a: number) => void;
export const __wbg_rawkinematiccharactercontroller_free: (a: number, b: number) => void;
export const rawkinematiccharactercontroller_new: (a: number) => number;
export const rawkinematiccharactercontroller_setUp: (a: number, b: number) => void;
export const rawkinematiccharactercontroller_normalNudgeFactor: (a: number) => number;
export const rawkinematiccharactercontroller_setNormalNudgeFactor: (a: number, b: number) => void;
export const rawkinematiccharactercontroller_setOffset: (a: number, b: number) => void;
export const rawkinematiccharactercontroller_slideEnabled: (a: number) => number;
export const rawkinematiccharactercontroller_setSlideEnabled: (a: number, b: number) => void;
export const rawkinematiccharactercontroller_autostepMaxHeight: (a: number) => number;
export const rawkinematiccharactercontroller_autostepMinWidth: (a: number) => number;
export const rawkinematiccharactercontroller_autostepIncludesDynamicBodies: (a: number) => number;
export const rawkinematiccharactercontroller_autostepEnabled: (a: number) => number;
export const rawkinematiccharactercontroller_enableAutostep: (a: number, b: number, c: number, d: number) => void;
export const rawkinematiccharactercontroller_disableAutostep: (a: number) => void;
export const rawkinematiccharactercontroller_maxSlopeClimbAngle: (a: number) => number;
export const rawkinematiccharactercontroller_setMaxSlopeClimbAngle: (a: number, b: number) => void;
export const rawkinematiccharactercontroller_minSlopeSlideAngle: (a: number) => number;
export const rawkinematiccharactercontroller_setMinSlopeSlideAngle: (a: number, b: number) => void;
export const rawkinematiccharactercontroller_snapToGroundDistance: (a: number) => number;
export const rawkinematiccharactercontroller_enableSnapToGround: (a: number, b: number) => void;
export const rawkinematiccharactercontroller_disableSnapToGround: (a: number) => void;
export const rawkinematiccharactercontroller_snapToGroundEnabled: (a: number) => number;
export const rawkinematiccharactercontroller_computeColliderMovement: (a: number, b: number, c: number, d: number, e: number, f: number, g: number, h: number, i: number, j: number, k: number, l: number) => void;
export const rawkinematiccharactercontroller_computedMovement: (a: number) => number;
export const rawkinematiccharactercontroller_computedGrounded: (a: number) => number;
export const rawkinematiccharactercontroller_numComputedCollisions: (a: number) => number;
export const rawkinematiccharactercontroller_computedCollision: (a: number, b: number, c: number) => number;
export const __wbg_rawcharactercollision_free: (a: number, b: number) => void;
export const rawcharactercollision_new: () => number;
export const rawcharactercollision_handle: (a: number) => number;
export const rawcharactercollision_translationDeltaApplied: (a: number) => number;
export const rawcharactercollision_translationDeltaRemaining: (a: number) => number;
export const rawcharactercollision_toi: (a: number) => number;
export const rawcharactercollision_worldWitness1: (a: number) => number;
export const rawcharactercollision_worldWitness2: (a: number) => number;
export const rawcharactercollision_worldNormal1: (a: number) => number;
export const rawcharactercollision_worldNormal2: (a: number) => number;
export const __wbg_rawpidcontroller_free: (a: number, b: number) => void;
export const rawpidcontroller_new: (a: number, b: number, c: number, d: number) => number;
export const rawpidcontroller_set_kp: (a: number, b: number, c: number) => void;
export const rawpidcontroller_set_ki: (a: number, b: number, c: number) => void;
export const rawpidcontroller_set_kd: (a: number, b: number, c: number) => void;
export const rawpidcontroller_set_axes_mask: (a: number, b: number) => void;
export const rawpidcontroller_reset_integrals: (a: number) => void;
export const rawpidcontroller_apply_linear_correction: (a: number, b: number, c: number, d: number, e: number, f: number) => void;
export const rawpidcontroller_apply_angular_correction: (a: number, b: number, c: number, d: number, e: number, f: number) => void;
export const rawpidcontroller_linear_correction: (a: number, b: number, c: number, d: number, e: number, f: number) => number;
export const rawpidcontroller_angular_correction: (a: number, b: number, c: number, d: number, e: number, f: number) => number;
export const __wbg_rawdynamicraycastvehiclecontroller_free: (a: number, b: number) => void;
export const rawdynamicraycastvehiclecontroller_new: (a: number) => number;
export const rawdynamicraycastvehiclecontroller_current_vehicle_speed: (a: number) => number;
export const rawdynamicraycastvehiclecontroller_chassis: (a: number) => number;
export const rawdynamicraycastvehiclecontroller_index_up_axis: (a: number) => number;
export const rawdynamicraycastvehiclecontroller_set_index_up_axis: (a: number, b: number) => void;
export const rawdynamicraycastvehiclecontroller_index_forward_axis: (a: number) => number;
export const rawdynamicraycastvehiclecontroller_set_index_forward_axis: (a: number, b: number) => void;
export const rawdynamicraycastvehiclecontroller_add_wheel: (a: number, b: number, c: number, d: number, e: number, f: number) => void;
export const rawdynamicraycastvehiclecontroller_num_wheels: (a: number) => number;
export const rawdynamicraycastvehiclecontroller_update_vehicle: (a: number, b: number, c: number, d: number, e: number, f: number, g: number, h: number) => void;
export const rawdynamicraycastvehiclecontroller_wheel_chassis_connection_point_cs: (a: number, b: number) => number;
export const rawdynamicraycastvehiclecontroller_set_wheel_chassis_connection_point_cs: (a: number, b: number, c: number) => void;
export const rawdynamicraycastvehiclecontroller_wheel_suspension_rest_length: (a: number, b: number) => number;
export const rawdynamicraycastvehiclecontroller_set_wheel_suspension_rest_length: (a: number, b: number, c: number) => void;
export const rawdynamicraycastvehiclecontroller_wheel_max_suspension_travel: (a: number, b: number) => number;
export const rawdynamicraycastvehiclecontroller_set_wheel_max_suspension_travel: (a: number, b: number, c: number) => void;
export const rawdynamicraycastvehiclecontroller_wheel_radius: (a: number, b: number) => number;
export const rawdynamicraycastvehiclecontroller_set_wheel_radius: (a: number, b: number, c: number) => void;
export const rawdynamicraycastvehiclecontroller_wheel_suspension_stiffness: (a: number, b: number) => number;
export const rawdynamicraycastvehiclecontroller_set_wheel_suspension_stiffness: (a: number, b: number, c: number) => void;
export const rawdynamicraycastvehiclecontroller_wheel_suspension_compression: (a: number, b: number) => number;
export const rawdynamicraycastvehiclecontroller_set_wheel_suspension_compression: (a: number, b: number, c: number) => void;
export const rawdynamicraycastvehiclecontroller_wheel_suspension_relaxation: (a: number, b: number) => number;
export const rawdynamicraycastvehiclecontroller_set_wheel_suspension_relaxation: (a: number, b: number, c: number) => void;
export const rawdynamicraycastvehiclecontroller_wheel_max_suspension_force: (a: number, b: number) => number;
export const rawdynamicraycastvehiclecontroller_set_wheel_max_suspension_force: (a: number, b: number, c: number) => void;
export const rawdynamicraycastvehiclecontroller_wheel_brake: (a: number, b: number) => number;
export const rawdynamicraycastvehiclecontroller_set_wheel_brake: (a: number, b: number, c: number) => void;
export const rawdynamicraycastvehiclecontroller_wheel_steering: (a: number, b: number) => number;
export const rawdynamicraycastvehiclecontroller_set_wheel_steering: (a: number, b: number, c: number) => void;
export const rawdynamicraycastvehiclecontroller_wheel_engine_force: (a: number, b: number) => number;
export const rawdynamicraycastvehiclecontroller_set_wheel_engine_force: (a: number, b: number, c: number) => void;
export const rawdynamicraycastvehiclecontroller_wheel_direction_cs: (a: number, b: number) => number;
export const rawdynamicraycastvehiclecontroller_set_wheel_direction_cs: (a: number, b: number, c: number) => void;
export const rawdynamicraycastvehiclecontroller_wheel_axle_cs: (a: number, b: number) => number;
export const rawdynamicraycastvehiclecontroller_set_wheel_axle_cs: (a: number, b: number, c: number) => void;
export const rawdynamicraycastvehiclecontroller_wheel_friction_slip: (a: number, b: number) => number;
export const rawdynamicraycastvehiclecontroller_set_wheel_friction_slip: (a: number, b: number, c: number) => void;
export const rawdynamicraycastvehiclecontroller_wheel_side_friction_stiffness: (a: number, b: number) => number;
export const rawdynamicraycastvehiclecontroller_set_wheel_side_friction_stiffness: (a: number, b: number, c: number) => void;
export const rawdynamicraycastvehiclecontroller_wheel_rotation: (a: number, b: number) => number;
export const rawdynamicraycastvehiclecontroller_wheel_forward_impulse: (a: number, b: number) => number;
export const rawdynamicraycastvehiclecontroller_wheel_side_impulse: (a: number, b: number) => number;
export const rawdynamicraycastvehiclecontroller_wheel_suspension_force: (a: number, b: number) => number;
export const rawdynamicraycastvehiclecontroller_wheel_contact_normal_ws: (a: number, b: number) => number;
export const rawdynamicraycastvehiclecontroller_wheel_contact_point_ws: (a: number, b: number) => number;
export const rawdynamicraycastvehiclecontroller_wheel_suspension_length: (a: number, b: number) => number;
export const rawdynamicraycastvehiclecontroller_wheel_hard_point_ws: (a: number, b: number) => number;
export const rawdynamicraycastvehiclecontroller_wheel_is_in_contact: (a: number, b: number) => number;
export const rawdynamicraycastvehiclecontroller_wheel_ground_object: (a: number, b: number, c: number) => void;
export const __wbg_rawccdsolver_free: (a: number, b: number) => void;
export const rawccdsolver_new: () => number;
export const rawimpulsejointset_jointType: (a: number, b: number) => number;
export const rawimpulsejointset_jointBodyHandle1: (a: number, b: number) => number;
export const rawimpulsejointset_jointBodyHandle2: (a: number, b: number) => number;
export const rawimpulsejointset_jointFrameX1: (a: number, b: number) => number;
export const rawimpulsejointset_jointFrameX2: (a: number, b: number) => number;
export const rawimpulsejointset_jointAnchor1: (a: number, b: number) => number;
export const rawimpulsejointset_jointAnchor2: (a: number, b: number) => number;
export const rawimpulsejointset_jointSetAnchor1: (a: number, b: number, c: number) => void;
export const rawimpulsejointset_jointSetAnchor2: (a: number, b: number, c: number) => void;
export const rawimpulsejointset_jointContactsEnabled: (a: number, b: number) => number;
export const rawimpulsejointset_jointSetContactsEnabled: (a: number, b: number, c: number) => void;
export const rawimpulsejointset_jointLimitsEnabled: (a: number, b: number, c: number) => number;
export const rawimpulsejointset_jointLimitsMin: (a: number, b: number, c: number) => number;
export const rawimpulsejointset_jointLimitsMax: (a: number, b: number, c: number) => number;
export const rawimpulsejointset_jointSetLimits: (a: number, b: number, c: number, d: number, e: number) => void;
export const rawimpulsejointset_jointConfigureMotorModel: (a: number, b: number, c: number, d: number) => void;
export const rawimpulsejointset_jointConfigureMotorVelocity: (a: number, b: number, c: number, d: number, e: number) => void;
export const rawimpulsejointset_jointConfigureMotorPosition: (a: number, b: number, c: number, d: number, e: number, f: number) => void;
export const rawimpulsejointset_jointConfigureMotor: (a: number, b: number, c: number, d: number, e: number, f: number, g: number) => void;
export const __wbg_rawimpulsejointset_free: (a: number, b: number) => void;
export const rawimpulsejointset_new: () => number;
export const rawimpulsejointset_createJoint: (a: number, b: number, c: number, d: number, e: number) => number;
export const rawimpulsejointset_remove: (a: number, b: number, c: number) => void;
export const rawimpulsejointset_len: (a: number) => number;
export const rawimpulsejointset_contains: (a: number, b: number) => number;
export const rawimpulsejointset_forEachJointHandle: (a: number, b: number) => void;
export const rawimpulsejointset_forEachJointAttachedToRigidBody: (a: number, b: number, c: number) => void;
export const __wbg_rawintegrationparameters_free: (a: number, b: number) => void;
export const rawintegrationparameters_new: () => number;
export const rawintegrationparameters_dt: (a: number) => number;
export const rawintegrationparameters_contact_erp: (a: number) => number;
export const rawintegrationparameters_normalizedAllowedLinearError: (a: number) => number;
export const rawintegrationparameters_numSolverIterations: (a: number) => number;
export const rawintegrationparameters_numAdditionalFrictionIterations: (a: number) => number;
export const rawintegrationparameters_numInternalPgsIterations: (a: number) => number;
export const rawintegrationparameters_maxCcdSubsteps: (a: number) => number;
export const rawintegrationparameters_lengthUnit: (a: number) => number;
export const rawintegrationparameters_set_dt: (a: number, b: number) => void;
export const rawintegrationparameters_set_contact_natural_frequency: (a: number, b: number) => void;
export const rawintegrationparameters_set_normalizedAllowedLinearError: (a: number, b: number) => void;
export const rawintegrationparameters_set_normalizedPredictionDistance: (a: number, b: number) => void;
export const rawintegrationparameters_set_numSolverIterations: (a: number, b: number) => void;
export const rawintegrationparameters_set_numAdditionalFrictionIterations: (a: number, b: number) => void;
export const rawintegrationparameters_set_numInternalPgsIterations: (a: number, b: number) => void;
export const rawintegrationparameters_set_minIslandSize: (a: number, b: number) => void;
export const rawintegrationparameters_set_maxCcdSubsteps: (a: number, b: number) => void;
export const rawintegrationparameters_set_lengthUnit: (a: number, b: number) => void;
export const rawintegrationparameters_switchToStandardPgsSolver: (a: number) => void;
export const rawintegrationparameters_switchToSmallStepsPgsSolver: (a: number) => void;
export const rawintegrationparameters_switchToSmallStepsPgsSolverWithoutWarmstart: (a: number) => void;
export const __wbg_rawislandmanager_free: (a: number, b: number) => void;
export const rawislandmanager_new: () => number;
export const rawislandmanager_forEachActiveRigidBodyHandle: (a: number, b: number) => void;
export const __wbg_rawgenericjoint_free: (a: number, b: number) => void;
export const rawgenericjoint_generic: (a: number, b: number, c: number, d: number) => number;
export const rawgenericjoint_spring: (a: number, b: number, c: number, d: number, e: number) => number;
export const rawgenericjoint_rope: (a: number, b: number, c: number) => number;
export const rawgenericjoint_spherical: (a: number, b: number) => number;
export const rawgenericjoint_prismatic: (a: number, b: number, c: number, d: number, e: number, f: number) => number;
export const rawgenericjoint_fixed: (a: number, b: number, c: number, d: number) => number;
export const rawgenericjoint_revolute: (a: number, b: number, c: number) => number;
export const rawmultibodyjointset_jointType: (a: number, b: number) => number;
export const rawmultibodyjointset_jointFrameX1: (a: number, b: number) => number;
export const rawmultibodyjointset_jointFrameX2: (a: number, b: number) => number;
export const rawmultibodyjointset_jointAnchor1: (a: number, b: number) => number;
export const rawmultibodyjointset_jointAnchor2: (a: number, b: number) => number;
export const rawmultibodyjointset_jointContactsEnabled: (a: number, b: number) => number;
export const rawmultibodyjointset_jointSetContactsEnabled: (a: number, b: number, c: number) => void;
export const rawmultibodyjointset_jointLimitsEnabled: (a: number, b: number, c: number) => number;
export const rawmultibodyjointset_jointLimitsMin: (a: number, b: number, c: number) => number;
export const rawmultibodyjointset_jointLimitsMax: (a: number, b: number, c: number) => number;
export const __wbg_rawmultibodyjointset_free: (a: number, b: number) => void;
export const rawmultibodyjointset_new: () => number;
export const rawmultibodyjointset_createJoint: (a: number, b: number, c: number, d: number, e: number) => number;
export const rawmultibodyjointset_remove: (a: number, b: number, c: number) => void;
export const rawmultibodyjointset_contains: (a: number, b: number) => number;
export const rawmultibodyjointset_forEachJointHandle: (a: number, b: number) => void;
export const rawmultibodyjointset_forEachJointAttachedToRigidBody: (a: number, b: number, c: number) => void;
export const rawrigidbodyset_rbTranslation: (a: number, b: number) => number;
export const rawrigidbodyset_rbRotation: (a: number, b: number) => number;
export const rawrigidbodyset_rbSleep: (a: number, b: number) => void;
export const rawrigidbodyset_rbIsSleeping: (a: number, b: number) => number;
export const rawrigidbodyset_rbIsMoving: (a: number, b: number) => number;
export const rawrigidbodyset_rbNextTranslation: (a: number, b: number) => number;
export const rawrigidbodyset_rbNextRotation: (a: number, b: number) => number;
export const rawrigidbodyset_rbSetTranslation: (a: number, b: number, c: number, d: number, e: number, f: number) => void;
export const rawrigidbodyset_rbSetRotation: (a: number, b: number, c: number, d: number, e: number, f: number, g: number) => void;
export const rawrigidbodyset_rbSetLinvel: (a: number, b: number, c: number, d: number) => void;
export const rawrigidbodyset_rbSetAngvel: (a: number, b: number, c: number, d: number) => void;
export const rawrigidbodyset_rbSetNextKinematicTranslation: (a: number, b: number, c: number, d: number, e: number) => void;
export const rawrigidbodyset_rbSetNextKinematicRotation: (a: number, b: number, c: number, d: number, e: number, f: number) => void;
export const rawrigidbodyset_rbRecomputeMassPropertiesFromColliders: (a: number, b: number, c: number) => void;
export const rawrigidbodyset_rbSetAdditionalMass: (a: number, b: number, c: number, d: number) => void;
export const rawrigidbodyset_rbSetAdditionalMassProperties: (a: number, b: number, c: number, d: number, e: number, f: number, g: number) => void;
export const rawrigidbodyset_rbLinvel: (a: number, b: number) => number;
export const rawrigidbodyset_rbAngvel: (a: number, b: number) => number;
export const rawrigidbodyset_rbLockTranslations: (a: number, b: number, c: number, d: number) => void;
export const rawrigidbodyset_rbSetEnabledTranslations: (a: number, b: number, c: number, d: number, e: number, f: number) => void;
export const rawrigidbodyset_rbLockRotations: (a: number, b: number, c: number, d: number) => void;
export const rawrigidbodyset_rbSetEnabledRotations: (a: number, b: number, c: number, d: number, e: number, f: number) => void;
export const rawrigidbodyset_rbDominanceGroup: (a: number, b: number) => number;
export const rawrigidbodyset_rbSetDominanceGroup: (a: number, b: number, c: number) => void;
export const rawrigidbodyset_rbEnableCcd: (a: number, b: number, c: number) => void;
export const rawrigidbodyset_rbSetSoftCcdPrediction: (a: number, b: number, c: number) => void;
export const rawrigidbodyset_rbMass: (a: number, b: number) => number;
export const rawrigidbodyset_rbInvMass: (a: number, b: number) => number;
export const rawrigidbodyset_rbEffectiveInvMass: (a: number, b: number) => number;
export const rawrigidbodyset_rbLocalCom: (a: number, b: number) => number;
export const rawrigidbodyset_rbWorldCom: (a: number, b: number) => number;
export const rawrigidbodyset_rbInvPrincipalInertiaSqrt: (a: number, b: number) => number;
export const rawrigidbodyset_rbPrincipalInertiaLocalFrame: (a: number, b: number) => number;
export const rawrigidbodyset_rbPrincipalInertia: (a: number, b: number) => number;
export const rawrigidbodyset_rbEffectiveWorldInvInertiaSqrt: (a: number, b: number) => number;
export const rawrigidbodyset_rbEffectiveAngularInertia: (a: number, b: number) => number;
export const rawrigidbodyset_rbWakeUp: (a: number, b: number) => void;
export const rawrigidbodyset_rbIsCcdEnabled: (a: number, b: number) => number;
export const rawrigidbodyset_rbSoftCcdPrediction: (a: number, b: number) => number;
export const rawrigidbodyset_rbNumColliders: (a: number, b: number) => number;
export const rawrigidbodyset_rbCollider: (a: number, b: number, c: number) => number;
export const rawrigidbodyset_rbBodyType: (a: number, b: number) => number;
export const rawrigidbodyset_rbSetBodyType: (a: number, b: number, c: number, d: number) => void;
export const rawrigidbodyset_rbIsFixed: (a: number, b: number) => number;
export const rawrigidbodyset_rbIsKinematic: (a: number, b: number) => number;
export const rawrigidbodyset_rbIsDynamic: (a: number, b: number) => number;
export const rawrigidbodyset_rbLinearDamping: (a: number, b: number) => number;
export const rawrigidbodyset_rbAngularDamping: (a: number, b: number) => number;
export const rawrigidbodyset_rbSetLinearDamping: (a: number, b: number, c: number) => void;
export const rawrigidbodyset_rbSetAngularDamping: (a: number, b: number, c: number) => void;
export const rawrigidbodyset_rbSetEnabled: (a: number, b: number, c: number) => void;
export const rawrigidbodyset_rbIsEnabled: (a: number, b: number) => number;
export const rawrigidbodyset_rbGravityScale: (a: number, b: number) => number;
export const rawrigidbodyset_rbSetGravityScale: (a: number, b: number, c: number, d: number) => void;
export const rawrigidbodyset_rbResetForces: (a: number, b: number, c: number) => void;
export const rawrigidbodyset_rbResetTorques: (a: number, b: number, c: number) => void;
export const rawrigidbodyset_rbAddForce: (a: number, b: number, c: number, d: number) => void;
export const rawrigidbodyset_rbApplyImpulse: (a: number, b: number, c: number, d: number) => void;
export const rawrigidbodyset_rbAddTorque: (a: number, b: number, c: number, d: number) => void;
export const rawrigidbodyset_rbApplyTorqueImpulse: (a: number, b: number, c: number, d: number) => void;
export const rawrigidbodyset_rbAddForceAtPoint: (a: number, b: number, c: number, d: number, e: number) => void;
export const rawrigidbodyset_rbApplyImpulseAtPoint: (a: number, b: number, c: number, d: number, e: number) => void;
export const rawrigidbodyset_rbAdditionalSolverIterations: (a: number, b: number) => number;
export const rawrigidbodyset_rbSetAdditionalSolverIterations: (a: number, b: number, c: number) => void;
export const rawrigidbodyset_rbUserData: (a: number, b: number) => number;
export const rawrigidbodyset_rbSetUserData: (a: number, b: number, c: number) => void;
export const rawrigidbodyset_rbUserForce: (a: number, b: number) => number;
export const rawrigidbodyset_rbUserTorque: (a: number, b: number) => number;
export const __wbg_rawrigidbodyset_free: (a: number, b: number) => void;
export const rawrigidbodyset_new: () => number;
export const rawrigidbodyset_createRigidBody: (a: number, b: number, c: number, d: number, e: number, f: number, g: number, h: number, i: number, j: number, k: number, l: number, m: number, n: number, o: number, p: number, q: number, r: number, s: number, t: number, u: number, v: number, w: number, x: number, y: number, z: number, a1: number) => number;
export const rawrigidbodyset_remove: (a: number, b: number, c: number, d: number, e: number, f: number) => void;
export const rawrigidbodyset_contains: (a: number, b: number) => number;
export const rawrigidbodyset_forEachRigidBodyHandle: (a: number, b: number) => void;
export const rawrigidbodyset_propagateModifiedBodyPositionsToColliders: (a: number, b: number) => void;
export const __wbg_rawbroadphase_free: (a: number, b: number) => void;
export const rawbroadphase_new: () => number;
export const rawcolliderset_coTranslation: (a: number, b: number) => number;
export const rawcolliderset_coRotation: (a: number, b: number) => number;
export const rawcolliderset_coSetTranslation: (a: number, b: number, c: number, d: number, e: number) => void;
export const rawcolliderset_coSetTranslationWrtParent: (a: number, b: number, c: number, d: number, e: number) => void;
export const rawcolliderset_coSetRotation: (a: number, b: number, c: number, d: number, e: number, f: number) => void;
export const rawcolliderset_coSetRotationWrtParent: (a: number, b: number, c: number, d: number, e: number, f: number) => void;
export const rawcolliderset_coIsSensor: (a: number, b: number) => number;
export const rawcolliderset_coShapeType: (a: number, b: number) => number;
export const rawcolliderset_coHalfspaceNormal: (a: number, b: number) => number;
export const rawcolliderset_coHalfExtents: (a: number, b: number) => number;
export const rawcolliderset_coSetHalfExtents: (a: number, b: number, c: number) => void;
export const rawcolliderset_coRadius: (a: number, b: number) => number;
export const rawcolliderset_coSetRadius: (a: number, b: number, c: number) => void;
export const rawcolliderset_coHalfHeight: (a: number, b: number) => number;
export const rawcolliderset_coSetHalfHeight: (a: number, b: number, c: number) => void;
export const rawcolliderset_coRoundRadius: (a: number, b: number) => number;
export const rawcolliderset_coSetRoundRadius: (a: number, b: number, c: number) => void;
export const rawcolliderset_coVertices: (a: number, b: number, c: number) => void;
export const rawcolliderset_coIndices: (a: number, b: number, c: number) => void;
export const rawcolliderset_coTriMeshFlags: (a: number, b: number) => number;
export const rawcolliderset_coHeightFieldFlags: (a: number, b: number) => number;
export const rawcolliderset_coHeightfieldHeights: (a: number, b: number, c: number) => void;
export const rawcolliderset_coHeightfieldScale: (a: number, b: number) => number;
export const rawcolliderset_coHeightfieldNRows: (a: number, b: number) => number;
export const rawcolliderset_coHeightfieldNCols: (a: number, b: number) => number;
export const rawcolliderset_coParent: (a: number, b: number, c: number) => void;
export const rawcolliderset_coSetEnabled: (a: number, b: number, c: number) => void;
export const rawcolliderset_coIsEnabled: (a: number, b: number) => number;
export const rawcolliderset_coSetContactSkin: (a: number, b: number, c: number) => void;
export const rawcolliderset_coContactSkin: (a: number, b: number) => number;
export const rawcolliderset_coFriction: (a: number, b: number) => number;
export const rawcolliderset_coRestitution: (a: number, b: number) => number;
export const rawcolliderset_coDensity: (a: number, b: number) => number;
export const rawcolliderset_coMass: (a: number, b: number) => number;
export const rawcolliderset_coVolume: (a: number, b: number) => number;
export const rawcolliderset_coCollisionGroups: (a: number, b: number) => number;
export const rawcolliderset_coSolverGroups: (a: number, b: number) => number;
export const rawcolliderset_coActiveHooks: (a: number, b: number) => number;
export const rawcolliderset_coActiveCollisionTypes: (a: number, b: number) => number;
export const rawcolliderset_coActiveEvents: (a: number, b: number) => number;
export const rawcolliderset_coContactForceEventThreshold: (a: number, b: number) => number;
export const rawcolliderset_coContainsPoint: (a: number, b: number, c: number) => number;
export const rawcolliderset_coCastShape: (a: number, b: number, c: number, d: number, e: number, f: number, g: number, h: number, i: number, j: number) => number;
export const rawcolliderset_coCastCollider: (a: number, b: number, c: number, d: number, e: number, f: number, g: number, h: number) => number;
export const rawcolliderset_coIntersectsShape: (a: number, b: number, c: number, d: number, e: number) => number;
export const rawcolliderset_coContactShape: (a: number, b: number, c: number, d: number, e: number, f: number) => number;
export const rawcolliderset_coContactCollider: (a: number, b: number, c: number, d: number) => number;
export const rawcolliderset_coProjectPoint: (a: number, b: number, c: number, d: number) => number;
export const rawcolliderset_coIntersectsRay: (a: number, b: number, c: number, d: number, e: number) => number;
export const rawcolliderset_coCastRay: (a: number, b: number, c: number, d: number, e: number, f: number) => number;
export const rawcolliderset_coCastRayAndGetNormal: (a: number, b: number, c: number, d: number, e: number, f: number) => number;
export const rawcolliderset_coSetSensor: (a: number, b: number, c: number) => void;
export const rawcolliderset_coSetRestitution: (a: number, b: number, c: number) => void;
export const rawcolliderset_coSetFriction: (a: number, b: number, c: number) => void;
export const rawcolliderset_coFrictionCombineRule: (a: number, b: number) => number;
export const rawcolliderset_coSetFrictionCombineRule: (a: number, b: number, c: number) => void;
export const rawcolliderset_coRestitutionCombineRule: (a: number, b: number) => number;
export const rawcolliderset_coSetRestitutionCombineRule: (a: number, b: number, c: number) => void;
export const rawcolliderset_coSetCollisionGroups: (a: number, b: number, c: number) => void;
export const rawcolliderset_coSetSolverGroups: (a: number, b: number, c: number) => void;
export const rawcolliderset_coSetActiveHooks: (a: number, b: number, c: number) => void;
export const rawcolliderset_coSetActiveEvents: (a: number, b: number, c: number) => void;
export const rawcolliderset_coSetActiveCollisionTypes: (a: number, b: number, c: number) => void;
export const rawcolliderset_coSetShape: (a: number, b: number, c: number) => void;
export const rawcolliderset_coSetContactForceEventThreshold: (a: number, b: number, c: number) => void;
export const rawcolliderset_coSetDensity: (a: number, b: number, c: number) => void;
export const rawcolliderset_coSetMass: (a: number, b: number, c: number) => void;
export const rawcolliderset_coSetMassProperties: (a: number, b: number, c: number, d: number, e: number, f: number) => void;
export const __wbg_rawcolliderset_free: (a: number, b: number) => void;
export const rawcolliderset_new: () => number;
export const rawcolliderset_len: (a: number) => number;
export const rawcolliderset_contains: (a: number, b: number) => number;
export const rawcolliderset_createCollider: (a: number, b: number, c: number, d: number, e: number, f: number, g: number, h: number, i: number, j: number, k: number, l: number, m: number, n: number, o: number, p: number, q: number, r: number, s: number, t: number, u: number, v: number, w: number, x: number, y: number, z: number, a1: number) => void;
export const rawcolliderset_remove: (a: number, b: number, c: number, d: number, e: number) => void;
export const rawcolliderset_forEachColliderHandle: (a: number, b: number) => void;
export const __wbg_rawshapecontact_free: (a: number, b: number) => void;
export const __wbg_rawnarrowphase_free: (a: number, b: number) => void;
export const rawnarrowphase_new: () => number;
export const rawnarrowphase_contact_pairs_with: (a: number, b: number, c: number) => void;
export const rawnarrowphase_contact_pair: (a: number, b: number, c: number) => number;
export const rawnarrowphase_intersection_pairs_with: (a: number, b: number, c: number) => void;
export const rawnarrowphase_intersection_pair: (a: number, b: number, c: number) => number;
export const __wbg_rawcontactmanifold_free: (a: number, b: number) => void;
export const rawcontactpair_collider1: (a: number) => number;
export const rawcontactpair_collider2: (a: number) => number;
export const rawcontactpair_numContactManifolds: (a: number) => number;
export const rawcontactpair_contactManifold: (a: number, b: number) => number;
export const rawcontactmanifold_normal: (a: number) => number;
export const rawcontactmanifold_local_n1: (a: number) => number;
export const rawcontactmanifold_local_n2: (a: number) => number;
export const rawcontactmanifold_subshape1: (a: number) => number;
export const rawcontactmanifold_subshape2: (a: number) => number;
export const rawcontactmanifold_num_contacts: (a: number) => number;
export const rawcontactmanifold_contact_local_p1: (a: number, b: number) => number;
export const rawcontactmanifold_contact_local_p2: (a: number, b: number) => number;
export const rawcontactmanifold_contact_dist: (a: number, b: number) => number;
export const rawcontactmanifold_contact_fid1: (a: number, b: number) => number;
export const rawcontactmanifold_contact_fid2: (a: number, b: number) => number;
export const rawcontactmanifold_contact_impulse: (a: number, b: number) => number;
export const rawcontactmanifold_contact_tangent_impulse_x: (a: number, b: number) => number;
export const rawcontactmanifold_contact_tangent_impulse_y: (a: number, b: number) => number;
export const rawcontactmanifold_num_solver_contacts: (a: number) => number;
export const rawcontactmanifold_solver_contact_point: (a: number, b: number) => number;
export const rawcontactmanifold_solver_contact_dist: (a: number, b: number) => number;
export const rawcontactmanifold_solver_contact_friction: (a: number, b: number) => number;
export const rawcontactmanifold_solver_contact_restitution: (a: number, b: number) => number;
export const rawcontactmanifold_solver_contact_tangent_velocity: (a: number, b: number) => number;
export const __wbg_rawpointprojection_free: (a: number, b: number) => void;
export const rawpointprojection_point: (a: number) => number;
export const rawpointprojection_isInside: (a: number) => number;
export const __wbg_rawpointcolliderprojection_free: (a: number, b: number) => void;
export const rawpointcolliderprojection_colliderHandle: (a: number) => number;
export const rawpointcolliderprojection_point: (a: number) => number;
export const rawpointcolliderprojection_isInside: (a: number) => number;
export const rawpointcolliderprojection_featureType: (a: number) => number;
export const rawpointcolliderprojection_featureId: (a: number) => number;
export const __wbg_rawrayintersection_free: (a: number, b: number) => void;
export const __wbg_rawraycolliderhit_free: (a: number, b: number) => void;
export const __wbg_rawshape_free: (a: number, b: number) => void;
export const rawshape_cuboid: (a: number, b: number, c: number) => number;
export const rawshape_roundCuboid: (a: number, b: number, c: number, d: number) => number;
export const rawshape_ball: (a: number) => number;
export const rawshape_halfspace: (a: number) => number;
export const rawshape_capsule: (a: number, b: number) => number;
export const rawshape_cylinder: (a: number, b: number) => number;
export const rawshape_roundCylinder: (a: number, b: number, c: number) => number;
export const rawshape_cone: (a: number, b: number) => number;
export const rawshape_roundCone: (a: number, b: number, c: number) => number;
export const rawshape_polyline: (a: number, b: number, c: number, d: number) => number;
export const rawshape_trimesh: (a: number, b: number, c: number, d: number, e: number) => number;
export const rawshape_heightfield: (a: number, b: number, c: number, d: number, e: number, f: number) => number;
export const rawshape_segment: (a: number, b: number) => number;
export const rawshape_triangle: (a: number, b: number, c: number) => number;
export const rawshape_roundTriangle: (a: number, b: number, c: number, d: number) => number;
export const rawshape_convexHull: (a: number, b: number) => number;
export const rawshape_roundConvexHull: (a: number, b: number, c: number) => number;
export const rawshape_convexMesh: (a: number, b: number, c: number, d: number) => number;
export const rawshape_roundConvexMesh: (a: number, b: number, c: number, d: number, e: number) => number;
export const rawshape_castShape: (a: number, b: number, c: number, d: number, e: number, f: number, g: number, h: number, i: number, j: number, k: number) => number;
export const rawshape_intersectsShape: (a: number, b: number, c: number, d: number, e: number, f: number) => number;
export const rawshape_contactShape: (a: number, b: number, c: number, d: number, e: number, f: number, g: number) => number;
export const rawshape_containsPoint: (a: number, b: number, c: number, d: number) => number;
export const rawshape_projectPoint: (a: number, b: number, c: number, d: number, e: number) => number;
export const rawshape_intersectsRay: (a: number, b: number, c: number, d: number, e: number, f: number) => number;
export const rawshape_castRay: (a: number, b: number, c: number, d: number, e: number, f: number, g: number) => number;
export const rawshape_castRayAndGetNormal: (a: number, b: number, c: number, d: number, e: number, f: number, g: number) => number;
export const __wbg_rawshapecasthit_free: (a: number, b: number) => void;
export const rawshapecasthit_witness1: (a: number) => number;
export const rawshapecasthit_normal1: (a: number) => number;
export const rawshapecasthit_normal2: (a: number) => number;
export const __wbg_rawcollidershapecasthit_free: (a: number, b: number) => void;
export const rawcollidershapecasthit_time_of_impact: (a: number) => number;
export const rawcollidershapecasthit_witness1: (a: number) => number;
export const rawcollidershapecasthit_witness2: (a: number) => number;
export const rawrotation_new: (a: number, b: number, c: number, d: number) => number;
export const rawrotation_identity: () => number;
export const rawrotation_x: (a: number) => number;
export const rawrotation_w: (a: number) => number;
export const rawvector_zero: () => number;
export const rawvector_new: (a: number, b: number, c: number) => number;
export const rawvector_set_x: (a: number, b: number) => void;
export const rawvector_set_z: (a: number, b: number) => void;
export const rawvector_xyz: (a: number) => number;
export const rawvector_yxz: (a: number) => number;
export const rawvector_zxy: (a: number) => number;
export const rawvector_xzy: (a: number) => number;
export const rawvector_yzx: (a: number) => number;
export const rawvector_zyx: (a: number) => number;
export const rawsdpmatrix3_elements: (a: number) => number;
export const __wbg_rawdebugrenderpipeline_free: (a: number, b: number) => void;
export const rawdebugrenderpipeline_new: () => number;
export const rawdebugrenderpipeline_vertices: (a: number) => number;
export const rawdebugrenderpipeline_colors: (a: number) => number;
export const rawdebugrenderpipeline_render: (a: number, b: number, c: number, d: number, e: number, f: number) => void;
export const __wbg_raweventqueue_free: (a: number, b: number) => void;
export const __wbg_rawcontactforceevent_free: (a: number, b: number) => void;
export const rawcontactforceevent_collider2: (a: number) => number;
export const rawcontactforceevent_total_force: (a: number) => number;
export const rawcontactforceevent_total_force_magnitude: (a: number) => number;
export const rawcontactforceevent_max_force_direction: (a: number) => number;
export const rawcontactforceevent_max_force_magnitude: (a: number) => number;
export const raweventqueue_new: (a: number) => number;
export const raweventqueue_drainCollisionEvents: (a: number, b: number) => void;
export const raweventqueue_drainContactForceEvents: (a: number, b: number) => void;
export const raweventqueue_clear: (a: number) => void;
export const __wbg_rawphysicspipeline_free: (a: number, b: number) => void;
export const rawphysicspipeline_new: () => number;
export const rawphysicspipeline_step: (a: number, b: number, c: number, d: number, e: number, f: number, g: number, h: number, i: number, j: number, k: number) => void;
export const rawphysicspipeline_stepWithEvents: (a: number, b: number, c: number, d: number, e: number, f: number, g: number, h: number, i: number, j: number, k: number, l: number, m: number, n: number, o: number) => void;
export const rawquerypipeline_new: () => number;
export const rawquerypipeline_update: (a: number, b: number) => void;
export const rawquerypipeline_castRay: (a: number, b: number, c: number, d: number, e: number, f: number, g: number, h: number, i: number, j: number, k: number, l: number, m: number, n: number) => number;
export const rawquerypipeline_castRayAndGetNormal: (a: number, b: number, c: number, d: number, e: number, f: number, g: number, h: number, i: number, j: number, k: number, l: number, m: number, n: number) => number;
export const rawquerypipeline_intersectionsWithRay: (a: number, b: number, c: number, d: number, e: number, f: number, g: number, h: number, i: number, j: number, k: number, l: number, m: number, n: number, o: number) => void;
export const rawquerypipeline_intersectionWithShape: (a: number, b: number, c: number, d: number, e: number, f: number, g: number, h: number, i: number, j: number, k: number, l: number, m: number, n: number) => void;
export const rawquerypipeline_projectPoint: (a: number, b: number, c: number, d: number, e: number, f: number, g: number, h: number, i: number, j: number, k: number, l: number) => number;
export const rawquerypipeline_projectPointAndGetFeature: (a: number, b: number, c: number, d: number, e: number, f: number, g: number, h: number, i: number, j: number, k: number) => number;
export const rawquerypipeline_intersectionsWithPoint: (a: number, b: number, c: number, d: number, e: number, f: number, g: number, h: number, i: number, j: number, k: number, l: number) => void;
export const rawquerypipeline_castShape: (a: number, b: number, c: number, d: number, e: number, f: number, g: number, h: number, i: number, j: number, k: number, l: number, m: number, n: number, o: number, p: number, q: number) => number;
export const rawquerypipeline_intersectionsWithShape: (a: number, b: number, c: number, d: number, e: number, f: number, g: number, h: number, i: number, j: number, k: number, l: number, m: number, n: number) => void;
export const rawquerypipeline_collidersWithAabbIntersectingAabb: (a: number, b: number, c: number, d: number) => void;
export const __wbg_rawdeserializedworld_free: (a: number, b: number) => void;
export const rawdeserializedworld_takeGravity: (a: number) => number;
export const rawdeserializedworld_takeIntegrationParameters: (a: number) => number;
export const rawdeserializedworld_takeIslandManager: (a: number) => number;
export const rawdeserializedworld_takeBroadPhase: (a: number) => number;
export const rawdeserializedworld_takeNarrowPhase: (a: number) => number;
export const rawdeserializedworld_takeBodies: (a: number) => number;
export const rawdeserializedworld_takeColliders: (a: number) => number;
export const rawdeserializedworld_takeImpulseJoints: (a: number) => number;
export const rawdeserializedworld_takeMultibodyJoints: (a: number) => number;
export const __wbg_rawserializationpipeline_free: (a: number, b: number) => void;
export const rawserializationpipeline_new: () => number;
export const rawserializationpipeline_serializeAll: (a: number, b: number, c: number, d: number, e: number, f: number, g: number, h: number, i: number, j: number) => number;
export const rawserializationpipeline_deserializeAll: (a: number, b: number) => number;
export const rawkinematiccharactercontroller_offset: (a: number) => number;
export const rawintegrationparameters_minIslandSize: (a: number) => number;
export const rawrigidbodyset_len: (a: number) => number;
export const rawshapecontact_distance: (a: number) => number;
export const rawrayintersection_featureType: (a: number) => number;
export const rawraycolliderintersection_colliderHandle: (a: number) => number;
export const rawrayintersection_time_of_impact: (a: number) => number;
export const rawraycolliderintersection_featureType: (a: number) => number;
export const rawraycolliderhit_colliderHandle: (a: number) => number;
export const rawraycolliderintersection_time_of_impact: (a: number) => number;
export const rawcollidershapecasthit_colliderHandle: (a: number) => number;
export const rawraycolliderhit_timeOfImpact: (a: number) => number;
export const rawshapecasthit_time_of_impact: (a: number) => number;
export const rawrotation_y: (a: number) => number;
export const rawrotation_z: (a: number) => number;
export const rawvector_x: (a: number) => number;
export const rawvector_y: (a: number) => number;
export const rawvector_z: (a: number) => number;
export const rawcontactforceevent_collider1: (a: number) => number;
export const rawintegrationparameters_normalizedPredictionDistance: (a: number) => number;
export const rawcolliderset_isHandleValid: (a: number, b: number) => number;
export const rawrayintersection_featureId: (a: number) => number;
export const rawraycolliderintersection_featureId: (a: number) => number;
export const rawkinematiccharactercontroller_up: (a: number) => number;
export const rawshapecontact_normal2: (a: number) => number;
export const rawshapecontact_point1: (a: number) => number;
export const rawshapecontact_point2: (a: number) => number;
export const rawrayintersection_normal: (a: number) => number;
export const rawraycolliderintersection_normal: (a: number) => number;
export const rawshapecontact_normal1: (a: number) => number;
export const rawcollidershapecasthit_normal1: (a: number) => number;
export const rawcollidershapecasthit_normal2: (a: number) => number;
export const rawshapecasthit_witness2: (a: number) => number;
export const __wbg_rawcontactpair_free: (a: number, b: number) => void;
export const __wbg_rawraycolliderintersection_free: (a: number, b: number) => void;
export const __wbg_rawrotation_free: (a: number, b: number) => void;
export const __wbg_rawvector_free: (a: number, b: number) => void;
export const __wbg_rawsdpmatrix3_free: (a: number, b: number) => void;
export const __wbg_rawquerypipeline_free: (a: number, b: number) => void;
export const rawvector_set_y: (a: number, b: number) => void;
export const __wbindgen_export_0: (a: number) => void;
export const __wbindgen_add_to_stack_pointer: (a: number) => number;
export const __wbindgen_export_1: (a: number, b: number, c: number) => void;
export const __wbindgen_export_2: (a: number, b: number) => number;
