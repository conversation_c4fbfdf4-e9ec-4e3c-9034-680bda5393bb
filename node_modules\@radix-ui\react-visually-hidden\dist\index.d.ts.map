{"mappings": ";;;AAYA,0BAA0B,MAAM,wBAAwB,CAAC,OAAO,UAAU,IAAI,CAAC,CAAC;AAChF,oCAA8B,SAAQ,kBAAkB;CAAG;AAE3D,OAAA,MAAM,2GAuBL,CAAC;AAMF,OAAA,MAAM,iGAAqB,CAAC", "sources": ["packages/react/visually-hidden/src/packages/react/visually-hidden/src/VisuallyHidden.tsx", "packages/react/visually-hidden/src/packages/react/visually-hidden/src/index.ts", "packages/react/visually-hidden/src/index.ts"], "sourcesContent": [null, null, "export {\n  VisuallyHidden,\n  //\n  Root,\n} from './VisuallyHidden';\nexport type { VisuallyHiddenProps } from './VisuallyHidden';\n"], "names": [], "version": 3, "file": "index.d.ts.map"}