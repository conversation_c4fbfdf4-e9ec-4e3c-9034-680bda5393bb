import React, { useRef, useState } from 'react';
import { Canvas, extend, useThree, useFrame } from '@react-three/fiber';
import { Text, RoundedBox } from '@react-three/drei';
import { BallCollider, CuboidCollider, Physics, RigidBody, useRopeJoint, useSphericalJoint } from '@react-three/rapier';
import { MeshLineGeometry, MeshLineMaterial } from 'meshline';
import * as THREE from 'three';
import styled from 'styled-components';
import { personalInfo, contactInfo } from '../../data/portfolio';

// Extend React Three Fiber with MeshLine
extend({ MeshLineGeometry, MeshLineMaterial });

const CanvasContainer = styled.div`
  width: 100%;
  height: 100%;
  position: relative;
  background: transparent;
`;

const Badge = () => {
  const band = useRef<any>();
  const fixed = useRef<any>();
  const j1 = useRef<any>();
  const j2 = useRef<any>();
  const j3 = useRef<any>();
  const card = useRef<any>();
  
  const vec = new THREE.Vector3();
  const ang = new THREE.Vector3();
  const rot = new THREE.Vector3();
  const dir = new THREE.Vector3();
  
  const { width, height } = useThree((state) => state.size);
  const [curve] = useState(() => new THREE.CatmullRomCurve3([
    new THREE.Vector3(), new THREE.Vector3(), new THREE.Vector3(), new THREE.Vector3()
  ]));
  const [dragged, drag] = useState<false | THREE.Vector3>(false);
  const [isFlipped, setIsFlipped] = useState(false);

  // Physics joints - creating the hanging chain
  useRopeJoint(fixed, j1, [[0, 0, 0], [0, 0, 0], 1]);
  useRopeJoint(j1, j2, [[0, 0, 0], [0, 0, 0], 1]);
  useRopeJoint(j2, j3, [[0, 0, 0], [0, 0, 0], 1]);
  useSphericalJoint(j3, card, [[0, 0, 0], [0, 1.45, 0]]);

  useFrame((state) => {
    if (dragged && card.current) {
      // Handle dragging physics
      vec.set(state.pointer.x, state.pointer.y, 0.5).unproject(state.camera);
      dir.copy(vec).sub(state.camera.position).normalize();
      vec.add(dir.multiplyScalar(state.camera.position.length()));
      
      // Wake up all physics bodies
      [card, j1, j2, j3, fixed].forEach((ref) => ref.current?.wakeUp());
      
      // Set kinematic position
      card.current.setNextKinematicTranslation({ 
        x: vec.x - dragged.x, 
        y: vec.y - dragged.y, 
        z: vec.z - dragged.z 
      });
    }
    
    if (fixed.current && band.current) {
      // Calculate the hanging rope curve
      curve.points[0].copy(j3.current.translation());
      curve.points[1].copy(j2.current.translation());
      curve.points[2].copy(j1.current.translation());
      curve.points[3].copy(fixed.current.translation());
      band.current.geometry.setPoints(curve.getPoints(32));
      
      // Tilt the card back towards the screen for better visibility
      if (card.current) {
        ang.copy(card.current.angvel());
        rot.copy(card.current.rotation());
        card.current.setAngvel({ x: ang.x, y: ang.y - rot.y * 0.25, z: ang.z });
      }
    }
  });

  const handleCardClick = () => {
    setIsFlipped(!isFlipped);
  };

  const handlePointerDown = (e: any) => {
    e.target.setPointerCapture(e.pointerId);
    const offset = new THREE.Vector3().copy(e.point).sub(vec.copy(card.current.translation()));
    drag(offset);
  };

  const handlePointerUp = (e: any) => {
    e.target.releasePointerCapture(e.pointerId);
    drag(false);
  };

  return (
    <>
      <group position={[0, 4, 0]}>
        {/* Fixed anchor point */}
        <RigidBody ref={fixed} angularDamping={2} linearDamping={2} type="fixed" />
        
        {/* Chain joints */}
        <RigidBody position={[0.5, 0, 0]} ref={j1} angularDamping={2} linearDamping={2}>
          <BallCollider args={[0.1]} />
        </RigidBody>
        <RigidBody position={[1, 0, 0]} ref={j2} angularDamping={2} linearDamping={2}>
          <BallCollider args={[0.1]} />
        </RigidBody>
        <RigidBody position={[1.5, 0, 0]} ref={j3} angularDamping={2} linearDamping={2}>
          <BallCollider args={[0.1]} />
        </RigidBody>
        
        {/* The badge card */}
        <RigidBody 
          position={[2, 0, 0]} 
          ref={card} 
          angularDamping={2} 
          linearDamping={2} 
          type={dragged ? 'kinematicPosition' : 'dynamic'}
        >
          <CuboidCollider args={[0.8, 1.125, 0.01]} />
          
          <group
            scale={2.25}
            position={[0, -1.2, 0]}
            onPointerDown={handlePointerDown}
            onPointerUp={handlePointerUp}
            onClick={handleCardClick}
          >
            {/* Main Badge */}
            <RoundedBox
              args={[1.6, 2.25, 0.1]}
              radius={0.1}
              smoothness={4}
            >
              <meshPhysicalMaterial
                color={isFlipped ? "#1a1a2e" : "#2a1a3e"}
                metalness={0.5}
                roughness={0.3}
                clearcoat={1}
                clearcoatRoughness={0.15}
              />
            </RoundedBox>
            
            {/* Badge Border Glow */}
            <RoundedBox
              args={[1.65, 2.3, 0.08]}
              radius={0.1}
              smoothness={4}
              position={[0, 0, -0.02]}
            >
              <meshStandardMaterial
                color="#00ffff"
                emissive="#00ffff"
                emissiveIntensity={0.2}
                transparent
                opacity={0.3}
              />
            </RoundedBox>

            {!isFlipped ? (
              // Front Side
              <group>
                {/* Profile Circle */}
                <mesh position={[0, 0.6, 0.06]}>
                  <circleGeometry args={[0.35]} />
                  <meshStandardMaterial
                    color="#00ffff"
                    emissive="#00ffff"
                    emissiveIntensity={0.1}
                  />
                </mesh>
                
                {/* Profile Emoji */}
                <Text
                  position={[0, 0.6, 0.07]}
                  fontSize={0.5}
                  color="#ffffff"
                  anchorX="center"
                  anchorY="middle"
                >
                  👨‍💻
                </Text>
                
                {/* Name */}
                <Text
                  position={[0, 0.1, 0.06]}
                  fontSize={0.12}
                  color="#ffffff"
                  anchorX="center"
                  anchorY="middle"
                  maxWidth={1.4}
                  textAlign="center"
                >
                  {personalInfo.name}
                </Text>
                
                {/* Title */}
                <Text
                  position={[0, -0.1, 0.06]}
                  fontSize={0.08}
                  color="#00ffff"
                  anchorX="center"
                  anchorY="middle"
                  maxWidth={1.4}
                  textAlign="center"
                >
                  {personalInfo.title}
                </Text>
                
                {/* Experience Badge */}
                <RoundedBox
                  args={[1.2, 0.25, 0.05]}
                  radius={0.05}
                  position={[0, -0.4, 0.06]}
                >
                  <meshStandardMaterial
                    color="#ffff00"
                    emissive="#ffff00"
                    emissiveIntensity={0.1}
                  />
                </RoundedBox>
                
                <Text
                  position={[0, -0.4, 0.09]}
                  fontSize={0.06}
                  color="#000000"
                  anchorX="center"
                  anchorY="middle"
                >
                  {personalInfo.yearsOfExperience}+ Years Experience
                </Text>
                
                {/* Location */}
                <Text
                  position={[0, -0.7, 0.06]}
                  fontSize={0.06}
                  color="#ffffff"
                  anchorX="center"
                  anchorY="middle"
                  maxWidth={1.4}
                  textAlign="center"
                >
                  📍 {personalInfo.location}
                </Text>
                
                {/* Flip Indicator */}
                <Text
                  position={[0.6, -0.9, 0.06]}
                  fontSize={0.04}
                  color="#00ffff"
                  anchorX="center"
                  anchorY="middle"
                >
                  Click to flip →
                </Text>
              </group>
            ) : (
              // Back Side
              <group>
                {/* Contact Title */}
                <Text
                  position={[0, 0.8, 0.06]}
                  fontSize={0.15}
                  color="#ffffff"
                  anchorX="center"
                  anchorY="middle"
                >
                  Contact Info
                </Text>
                
                {/* Email */}
                <Text
                  position={[0, 0.4, 0.06]}
                  fontSize={0.06}
                  color="#00ffff"
                  anchorX="center"
                  anchorY="middle"
                >
                  EMAIL
                </Text>
                <Text
                  position={[0, 0.3, 0.06]}
                  fontSize={0.05}
                  color="#ffffff"
                  anchorX="center"
                  anchorY="middle"
                  maxWidth={1.4}
                >
                  {contactInfo.email}
                </Text>
                
                {/* GitHub */}
                <Text
                  position={[0, 0.1, 0.06]}
                  fontSize={0.06}
                  color="#00ffff"
                  anchorX="center"
                  anchorY="middle"
                >
                  GITHUB
                </Text>
                <Text
                  position={[0, 0, 0.06]}
                  fontSize={0.05}
                  color="#ffffff"
                  anchorX="center"
                  anchorY="middle"
                  maxWidth={1.4}
                >
                  {contactInfo.github.replace('https://', '')}
                </Text>
                
                {/* LinkedIn */}
                <Text
                  position={[0, -0.2, 0.06]}
                  fontSize={0.06}
                  color="#00ffff"
                  anchorX="center"
                  anchorY="middle"
                >
                  LINKEDIN
                </Text>
                <Text
                  position={[0, -0.3, 0.06]}
                  fontSize={0.05}
                  color="#ffffff"
                  anchorX="center"
                  anchorY="middle"
                  maxWidth={1.4}
                >
                  {contactInfo.linkedin.replace('https://', '')}
                </Text>
                
                {/* Flip Indicator */}
                <Text
                  position={[-0.6, -0.9, 0.06]}
                  fontSize={0.04}
                  color="#00ffff"
                  anchorX="center"
                  anchorY="middle"
                >
                  ← Click to flip
                </Text>
              </group>
            )}
          </group>
        </RigidBody>
      </group>
      
      {/* The hanging rope/lanyard */}
      <mesh ref={band}>
        <meshLineGeometry />
        <meshLineMaterial 
          transparent 
          opacity={0.8} 
          color="#666666" 
          depthTest={false} 
          resolution={[width, height]} 
          lineWidth={2} 
        />
      </mesh>
    </>
  );
};

export const HangingBadgeCanvas: React.FC = () => {
  return (
    <CanvasContainer>
      <Canvas camera={{ position: [0, 0, 13], fov: 25 }}>
        <ambientLight intensity={Math.PI} />
        <pointLight position={[10, 10, 10]} intensity={1} />
        <pointLight position={[-10, -10, -10]} intensity={0.5} color="#00ffff" />
        <spotLight
          position={[0, 10, 5]}
          angle={0.3}
          penumbra={1}
          intensity={1}
          castShadow
        />
        
        <Physics 
          interpolate 
          gravity={[0, -40, 0]} 
          timeStep={1 / 60}
        >
          <Badge />
        </Physics>
      </Canvas>
    </CanvasContainer>
  );
};
