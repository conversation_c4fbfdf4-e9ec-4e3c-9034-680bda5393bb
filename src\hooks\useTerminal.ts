import { useState, useCallback } from 'react';
import { TerminalLine, TerminalState } from '../types/terminal';
import { commands } from '../utils/commands';

const generateId = () => Math.random().toString(36).substr(2, 9);

const initialWelcomeMessage = `
<div style="color: #00ff00; font-weight: bold; font-size: 18px;">
╔══════════════════════════════════════════════════════════════╗
║                    WELCOME TO MY PORTFOLIO                   ║
╚══════════════════════════════════════════════════════════════╝
</div>

<div style="color: #00ffff;">
System initialized successfully...
Loading portfolio data...
</div>

<div style="color: #ffff00;">
Type 'help' to see available commands
Type 'about' to learn more about me
</div>

<div style="color: #ffffff;">
Ready for input.
</div>
`;

export const useTerminal = () => {
  const [state, setState] = useState<TerminalState>({
    lines: [
      {
        id: generateId(),
        type: 'output',
        content: initialWelcomeMessage,
        timestamp: new Date()
      }
    ],
    currentInput: '',
    history: [],
    historyIndex: -1,
    currentTheme: 'dark',
    isLoading: false
  });

  const addLine = useCallback((line: Omit<TerminalLine, 'id' | 'timestamp'>) => {
    setState(prev => ({
      ...prev,
      lines: [...prev.lines, {
        ...line,
        id: generateId(),
        timestamp: new Date()
      }]
    }));
  }, []);

  const processCommand = useCallback((input: string) => {
    const trimmedInput = input.trim();
    
    // Add input to history
    if (trimmedInput) {
      setState(prev => ({
        ...prev,
        history: [...prev.history, trimmedInput],
        historyIndex: -1
      }));
    }

    // Add input line
    addLine({
      type: 'input',
      content: `$ ${trimmedInput}`
    });

    if (!trimmedInput) return;

    // Parse command and arguments
    const [commandName, ...args] = trimmedInput.split(' ');
    const command = commands[commandName] || 
                   Object.values(commands).find(cmd => cmd.aliases?.includes(commandName));

    if (!command) {
      addLine({
        type: 'error',
        content: `Command not found: ${commandName}. Type 'help' for available commands.`
      });
      return;
    }

    try {
      const result = command.execute(args);
      
      // Handle special commands
      if (result.content === 'CLEAR_TERMINAL') {
        setState(prev => ({
          ...prev,
          lines: []
        }));
        return;
      }
      
      if (result.content === 'SHOW_HISTORY') {
        const historyContent = state.history.length > 0 
          ? state.history.map((cmd, index) => `${index + 1}  ${cmd}`).join('\n')
          : 'No commands in history';
        addLine({
          type: 'output',
          content: historyContent
        });
        return;
      }
      
      if (result.content.startsWith('CHANGE_THEME:')) {
        const themeName = result.content.split(':')[1];
        setState(prev => ({
          ...prev,
          currentTheme: themeName
        }));
        addLine({
          type: 'success',
          content: `Theme changed to: ${themeName}`
        });
        return;
      }

      addLine({
        type: result.type === 'html' ? 'output' : (result.type === 'text' ? 'output' : result.type),
        content: result.content
      });
    } catch (error) {
      addLine({
        type: 'error',
        content: `Error executing command: ${error instanceof Error ? error.message : 'Unknown error'}`
      });
    }
  }, [addLine, state.history]);

  const setCurrentInput = useCallback((input: string) => {
    setState(prev => ({ ...prev, currentInput: input }));
  }, []);

  const navigateHistory = useCallback((direction: 'up' | 'down') => {
    setState(prev => {
      const newIndex = direction === 'up' 
        ? Math.min(prev.historyIndex + 1, prev.history.length - 1)
        : Math.max(prev.historyIndex - 1, -1);
      
      const newInput = newIndex >= 0 ? prev.history[prev.history.length - 1 - newIndex] : '';
      
      return {
        ...prev,
        historyIndex: newIndex,
        currentInput: newInput
      };
    });
  }, []);

  const clearTerminal = useCallback(() => {
    setState(prev => ({
      ...prev,
      lines: []
    }));
  }, []);

  const changeTheme = useCallback((themeName: string) => {
    setState(prev => ({
      ...prev,
      currentTheme: themeName
    }));
  }, []);

  const getAutocompleteSuggestions = useCallback((input: string) => {
    const commandNames = Object.keys(commands);
    const aliases = Object.values(commands).flatMap(cmd => cmd.aliases || []);
    const allCommands = [...commandNames, ...aliases];
    
    return allCommands.filter(cmd => cmd.startsWith(input.toLowerCase()));
  }, []);

  return {
    ...state,
    addLine,
    processCommand,
    setCurrentInput,
    navigateHistory,
    clearTerminal,
    changeTheme,
    getAutocompleteSuggestions
  };
};
