{"name": "@floating-ui/dom", "version": "0.5.4", "@rollingversions": {"baseVersion": [0, 0, 0]}, "description": "Floating UI for the web", "publishConfig": {"access": "public"}, "main": "./dist/floating-ui.dom.umd.js", "module": "./dist/floating-ui.dom.esm.js", "unpkg": "./dist/floating-ui.dom.umd.min.js", "types": "./index.d.ts", "exports": {".": {"types": "./index.d.ts", "import": {"browser": {"development": "./dist/floating-ui.dom.browser.mjs", "default": "./dist/floating-ui.dom.browser.min.mjs"}, "default": "./dist/floating-ui.dom.mjs"}, "module": "./dist/floating-ui.dom.esm.js", "default": "./dist/floating-ui.dom.umd.js"}, "./package.json": "./package.json"}, "sideEffects": false, "files": ["dist/", "index.d.ts", "src/**/*.d.ts"], "browserslist": "> 0.5%, not dead, not IE 11", "scripts": {"dev": "parcel test/visual/index.html", "build": "NODE_ENV=build rollup -c", "test": "jest test/unit"}, "author": "atomiks", "license": "MIT", "bugs": "https://github.com/floating-ui/floating-ui", "repository": {"type": "git", "url": "https://github.com/floating-ui/floating-ui.git", "directory": "packages/dom"}, "homepage": "https://floating-ui.com", "keywords": ["tooltip", "popover", "dropdown", "menu", "popup", "positioning"], "dependencies": {"@floating-ui/core": "^0.7.3"}, "devDependencies": {"@babel/preset-env": "^7.16.4", "@babel/preset-typescript": "^7.16.0", "@playwright/test": "^1.16.3", "@rollup/plugin-replace": "^3.0.0", "@types/jest": "^27.0.3", "@types/react": "^18.0.1", "babel-plugin-annotate-pure-calls": "^0.4.0", "concurrently": "^6.4.0", "jest": "^27.3.1", "react": "^18.0.0", "react-dom": "^18.0.0", "react-router-dom": "^6.1.1", "rollup-plugin-terser": "^7.0.2", "serve": "^13.0.2", "ts-jest": "^27.0.7"}}