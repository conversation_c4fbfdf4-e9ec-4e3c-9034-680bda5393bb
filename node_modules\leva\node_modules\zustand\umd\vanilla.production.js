!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).zustandVanilla={})}(this,(function(e){"use strict";e.default=function(e){var t,n=new Set,r=function(e,r){var i="function"==typeof e?e(t):e;if(i!==t){var o=t;t=r?i:Object.assign({},t,i),n.forEach((function(e){return e(t,o)}))}},i=function(){return t},o={setState:r,getState:i,subscribe:function(e,r,o){return r||o?function(e,r,o){void 0===r&&(r=i),void 0===o&&(o=Object.is),console.warn("[DEPRECATED] Please use `subscribeWithSelector` middleware");var u=r(t);function f(){var n=r(t);if(!o(u,n)){var i=u;e(u=n,i)}}return n.add(f),function(){return n.delete(f)}}(e,r,o):(n.add(e),function(){return n.delete(e)})},destroy:function(){return n.clear()}};return t=e(r,i,o),o},Object.defineProperty(e,"__esModule",{value:!0})}));
