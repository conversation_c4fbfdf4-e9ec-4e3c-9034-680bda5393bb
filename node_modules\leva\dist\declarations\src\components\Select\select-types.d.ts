import type { LevaInputProps } from '../../types';
export declare type SelectSettings<U = unknown> = {
    options: Record<string, U> | U[];
};
export declare type InternalSelectSettings = {
    keys: string[];
    values: any[];
};
export declare type SelectInput<P = unknown, U = unknown> = {
    value?: P;
} & SelectSettings<U>;
export declare type SelectProps = LevaInputProps<any, InternalSelectSettings, number>;
