import type { InputWithSettings, LevaInputProps, NumberSettings } from '../../types';
export declare type InternalNumberSettings = {
    min: number;
    max: number;
    step: number;
    pad: number;
    initialValue: number;
    suffix?: string;
};
export declare type NumberInput = InputWithSettings<number | string, NumberSettings>;
export declare type NumberProps = LevaInputProps<number, InternalNumberSettings>;
export declare type RangeSliderProps = {
    value: number;
    onDrag: (v: number) => void;
} & InternalNumberSettings;
