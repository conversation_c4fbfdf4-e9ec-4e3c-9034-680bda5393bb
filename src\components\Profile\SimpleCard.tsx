import React, { useState } from 'react';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import { personalInfo, contactInfo } from '../../data/portfolio';

const CardContainer = styled(motion.div)`
  perspective: 1000px;
  width: 350px;
  height: 500px;
  margin: 0 auto;
`;

const Card = styled(motion.div)`
  width: 100%;
  height: 100%;
  position: relative;
  transform-style: preserve-3d;
  cursor: pointer;
`;

const CardFace = styled.div<{ isBack?: boolean }>`
  position: absolute;
  width: 100%;
  height: 100%;
  backface-visibility: hidden;
  border-radius: 20px;
  padding: 2rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  box-shadow: 
    0 20px 40px rgba(0, 0, 0, 0.3),
    0 0 20px rgba(0, 255, 255, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(0, 255, 255, 0.3);
  
  ${props => props.isBack && `
    transform: rotateY(180deg);
    background: linear-gradient(135deg, #2a5298 0%, #1e3c72 100%);
  `}

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(0, 255, 255, 0.1) 50%, transparent 70%);
    border-radius: 20px;
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  &:hover::before {
    opacity: 1;
  }
`;

const ProfileImage = styled.div`
  width: 120px;
  height: 120px;
  border-radius: 50%;
  background: linear-gradient(135deg, #00ffff 0%, #0080ff 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1.5rem;
  box-shadow: 0 10px 20px rgba(0, 255, 255, 0.3);
  position: relative;
  overflow: hidden;
  font-size: 3rem;

  &::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transform: rotate(45deg);
    animation: shine 3s infinite;
  }

  @keyframes shine {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    50% { transform: translateX(100%) translateY(100%) rotate(45deg); }
    100% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
  }
`;

const Name = styled.h1`
  color: #ffffff;
  font-size: 1.8rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  text-align: center;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
`;

const Title = styled.h2`
  color: #00ffff;
  font-size: 1.1rem;
  font-weight: 400;
  margin-bottom: 1rem;
  text-align: center;
  opacity: 0.9;
`;

const Bio = styled.p`
  color: #ffffff;
  font-size: 0.9rem;
  line-height: 1.6;
  text-align: center;
  opacity: 0.8;
  margin-bottom: 1.5rem;
`;

const InfoGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  width: 100%;
  margin-top: 1rem;
`;

const InfoItem = styled.div`
  text-align: center;
`;

const InfoLabel = styled.div`
  color: #00ffff;
  font-size: 0.8rem;
  font-weight: 600;
  margin-bottom: 0.25rem;
  text-transform: uppercase;
  letter-spacing: 1px;
`;

const InfoValue = styled.div`
  color: #ffffff;
  font-size: 1rem;
  font-weight: 500;
`;

const ContactList = styled.div`
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
`;

const ContactItem = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.5rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  border: 1px solid rgba(0, 255, 255, 0.2);
`;

const ContactLabel = styled.span`
  color: #00ffff;
  font-size: 0.9rem;
  font-weight: 600;
`;

const ContactValue = styled.span`
  color: #ffffff;
  font-size: 0.8rem;
  opacity: 0.9;
`;

const FlipIndicator = styled.div`
  position: absolute;
  bottom: 1rem;
  right: 1rem;
  color: #00ffff;
  font-size: 0.8rem;
  opacity: 0.7;
  animation: pulse 2s infinite;

  @keyframes pulse {
    0%, 100% { opacity: 0.7; }
    50% { opacity: 1; }
  }
`;

export const SimpleCard: React.FC = () => {
  const [isFlipped, setIsFlipped] = useState(false);

  const handleCardClick = () => {
    setIsFlipped(!isFlipped);
  };

  return (
    <CardContainer
      initial={{ opacity: 0, y: 50 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.8, delay: 0.2 }}
    >
      <Card
        animate={{ rotateY: isFlipped ? 180 : 0 }}
        transition={{ duration: 0.6 }}
        onClick={handleCardClick}
      >
        {/* Front Face */}
        <CardFace>
          <ProfileImage>👨‍💻</ProfileImage>
          <Name>{personalInfo.name}</Name>
          <Title>{personalInfo.title}</Title>
          <Bio>{personalInfo.bio}</Bio>
          
          <InfoGrid>
            <InfoItem>
              <InfoLabel>Experience</InfoLabel>
              <InfoValue>{personalInfo.yearsOfExperience}+ Years</InfoValue>
            </InfoItem>
            <InfoItem>
              <InfoLabel>Location</InfoLabel>
              <InfoValue>{personalInfo.location}</InfoValue>
            </InfoItem>
          </InfoGrid>
          
          <FlipIndicator>Click to flip →</FlipIndicator>
        </CardFace>

        {/* Back Face */}
        <CardFace isBack>
          <Name>Contact Info</Name>
          
          <ContactList>
            <ContactItem>
              <ContactLabel>Email</ContactLabel>
              <ContactValue>{contactInfo.email}</ContactValue>
            </ContactItem>
            
            {contactInfo.phone && (
              <ContactItem>
                <ContactLabel>Phone</ContactLabel>
                <ContactValue>{contactInfo.phone}</ContactValue>
              </ContactItem>
            )}
            
            <ContactItem>
              <ContactLabel>GitHub</ContactLabel>
              <ContactValue>{contactInfo.github.replace('https://', '')}</ContactValue>
            </ContactItem>
            
            <ContactItem>
              <ContactLabel>LinkedIn</ContactLabel>
              <ContactValue>{contactInfo.linkedin.replace('https://', '')}</ContactValue>
            </ContactItem>
            
            {contactInfo.website && (
              <ContactItem>
                <ContactLabel>Website</ContactLabel>
                <ContactValue>{contactInfo.website.replace('https://', '')}</ContactValue>
              </ContactItem>
            )}
          </ContactList>
          
          <FlipIndicator>← Click to flip</FlipIndicator>
        </CardFace>
      </Card>
    </CardContainer>
  );
};
