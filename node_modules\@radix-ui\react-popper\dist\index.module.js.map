{"mappings": ";;;;;;;;;;;A;;;;;;;;;;ACyBA,MAAMS,yCAAY,GAAG;IAAC,KAAD;IAAQ,OAAR;IAAiB,QAAjB;IAA2B,MAA3B;CAArB,AAAA;AACA,MAAMC,yCAAa,GAAG;IAAC,OAAD;IAAU,QAAV;IAAoB,KAApB;CAAtB,AAAA;AAKA;;oGAEA,CAEA,MAAMmB,iCAAW,GAAG,QAApB,AAAA;AAGA,MAAM,CAACC,yCAAD,EAAsB9B,uCAAtB,CAAA,GAA2CwB,yBAAkB,CAACK,iCAAD,CAAnE,AAAA;AAMA,MAAM,CAACE,oCAAD,EAAiBC,sCAAjB,CAAA,GAAqCF,yCAAmB,CAAqBD,iCAArB,CAA9D,AAAA;AAKA,MAAM5B,yCAA6B,GAAIgC,CAAAA,KAAD,GAAqC;IACzE,MAAM,E,eAAEC,aAAF,CAAA,E,UAAiBC,QAAAA,CAAAA,EAAjB,GAA8BF,KAApC,AAAM;IACN,MAAM,CAACG,MAAD,EAASC,SAAT,CAAA,GAAsB1B,eAAA,CAAkC,IAAlC,CAA5B,AAAA;IACA,OAAA,aACE,CAAA,oBAAA,CAAC,oCAAD,EADF;QACkB,KAAK,EAAEuB,aAAvB;QAAsC,MAAM,EAAEE,MAA9C;QAAsD,cAAc,EAAEC,SAAhB;KAAtD,EACGF,QADH,CADF,CACE;CAJJ,AAQC;AAED,aAAA,CAAA,MAAA,CAAA,MAAA,CAAA,yCAAA,EAAA;IAAA,WAAA,EAAA,iCAAA;CAAA,CAAA,CAAA;AAEA;;oGAEA,CAEA,MAAMI,iCAAW,GAAG,cAApB,AAAA;AAQA,MAAMrC,wCAAY,GAAA,aAAGS,CAAAA,iBAAA,CACnB,CAACsB,KAAD,EAAwCQ,YAAxC,GAAyD;IACvD,MAAM,E,eAAEP,aAAF,CAAA,E,YAAiBQ,UAAjB,CAAA,EAA6B,GAAGC,WAAH,EAA7B,GAAgDV,KAAtD,AAAM;IACN,MAAMW,OAAO,GAAGZ,sCAAgB,CAACO,iCAAD,EAAcL,aAAd,CAAhC,AAAA;IACA,MAAMW,GAAG,GAAGlC,aAAA,CAAkC,IAAlC,CAAZ,AAAA;IACA,MAAMoC,YAAY,GAAGxB,sBAAe,CAACkB,YAAD,EAAeI,GAAf,CAApC,AAAA;IAEAlC,gBAAA,CAAgB,IAAM;QACpB,yDAAA;QACA,uDAAA;QACA,mDAAA;QACAiC,OAAO,CAACK,cAAR,CAAuB,AAAAP,CAAAA,UAAU,KAAA,IAAV,IAAAA,UAAU,KAAA,KAAA,CAAV,GAAA,KAAA,CAAA,GAAAA,UAAU,CAAEQ,OAAZ,CAAA,IAAuBL,GAAG,CAACK,OAAlD,CAAAN,CAAAA;KAJF,CAKC,CAAA;IAED,OAAOF,UAAU,GAAG,IAAH,GAAA,aAAU,CAAA,oBAAA,CAAC,gBAAD,CAAW,GAAX,EAAA,oCAAA,CAAA,EAAA,EAAmBC,WAAnB,EAA3B;QAA2D,GAAG,EAAEI,YAAL;KAAhC,CAAA,CAA3B,CAA2B;CAdV,CAArB,AAeG;AAGH,aAAA,CAAA,MAAA,CAAA,MAAA,CAAA,wCAAA,EAAA;IAAA,WAAA,EAAA,iCAAA;CAAA,CAAA,CAAA;AAEA;;oGAEA,CAEA,MAAMI,kCAAY,GAAG,eAArB,AAAA;AAUA,MAAM,CAACC,2CAAD,EAAwBC,uCAAxB,CAAA,GACJvB,yCAAmB,CAA4BqB,kCAA5B,CADrB,AAAA;AAGA,MAAM,CAACG,6CAAD,EAA0BC,wCAA1B,CAAA,GAAgDzB,yCAAmB,CAACqB,kCAAD,EAAe;IACtFK,SAAS,EAAE,KAD2E;IAEtFC,iBAAiB,EAAE,IAAIC,GAAJ,EAAnBD;CAFuE,CAAzE,AAAwF;AAsBxF,MAAMtD,wCAAa,GAAA,aAAGQ,CAAAA,iBAAA,CACpB,CAACsB,KAAD,EAAyCQ,YAAzC,GAA0D;IAAA,IAAA,gBAAA,EAAA,iBAAA,EAAA,qBAAA,EAAA,sBAAA,EAAA,sBAAA,EAAA,oBAAA,EAAA,qBAAA,EAAA,sBAAA,AAAA;IACxD,MAAM,E,eACJP,aADI,CAAA,QAEJyB,IAAI,GAAG,QAFH,eAGJC,UAAU,GAAG,CAHT,UAIJC,KAAK,GAAG,QAJJ,gBAKJC,WAAW,GAAG,CALV,iBAMJC,YAAY,GAAG,CANX,sBAOJC,iBAAiB,GAAG,EAPhB,GAQJC,gBAAgB,EAAEC,oBAAoB,GAAG,CARrC,CAAA,UASJC,MAAM,GAAG,SATL,qBAUJC,gBAAgB,GAAG,KAVf,oBAWJC,eAAe,GAAG,IAXd,G,UAYJC,QAZI,CAAA,EAaJ,GAAGC,YAAH,EAbI,GAcFtC,KAdJ,AAAM;IAgBN,MAAMW,OAAO,GAAGZ,sCAAgB,CAACmB,kCAAD,EAAejB,aAAf,CAAhC,AAAA;IAEA,MAAM,CAACsC,OAAD,EAAUC,UAAV,CAAA,GAAwB9D,eAAA,CAAsC,IAAtC,CAA9B,AAAA;IACA,MAAMoC,YAAY,GAAGxB,sBAAe,CAACkB,YAAD,EAAgBiC,CAAAA,IAAD,GAAUD,UAAU,CAACC,IAAD,CAAnC;IAAA,CAApC,AAAA;IAEA,MAAM,CAACxD,KAAD,EAAQyD,QAAR,CAAA,GAAoBhE,eAAA,CAAuC,IAAvC,CAA1B,AAAA;IACA,MAAMiE,SAAS,GAAGhD,cAAO,CAACV,KAAD,CAAzB,AAAA;IACA,MAAM2D,UAAU,GAAA,AAAA,CAAA,gBAAA,GAAGD,SAAH,KAAA,IAAA,IAAGA,SAAH,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAGA,SAAS,CAAEE,KAAd,CAAA,KAAA,IAAA,IAAA,gBAAA,KAAA,KAAA,CAAA,GAAA,gBAAA,GAAuB,CAAvC,AAAA;IACA,MAAMC,WAAW,GAAA,AAAA,CAAA,iBAAA,GAAGH,SAAH,KAAA,IAAA,IAAGA,SAAH,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAGA,SAAS,CAAEI,MAAd,CAAA,KAAA,IAAA,IAAA,iBAAA,KAAA,KAAA,CAAA,GAAA,iBAAA,GAAwB,CAAzC,AAAA;IAEA,MAAMC,gBAAgB,GAAItB,IAAI,GAAIE,CAAAA,KAAK,KAAK,QAAV,GAAqB,GAAA,GAAMA,KAA3B,GAAmC,EAAvC,CAAA,AAA9B,AAAA;IAEA,MAAMI,gBAAgB,GACpB,OAAOC,oBAAP,KAAgC,QAAhC,GACIA,oBADJ,GAEI;QAAEgB,GAAG,EAAE,CAAP;QAAUC,KAAK,EAAE,CAAjB;QAAoBC,MAAM,EAAE,CAA5B;QAA+BC,IAAI,EAAE,CAArC;QAAwC,GAAGnB,oBAAH;KAH9C,AAGM;IAEN,MAAMoB,QAAQ,GAAGC,KAAK,CAACC,OAAN,CAAcxB,iBAAd,CAAA,GAAmCA,iBAAnC,GAAuD;QAACA,iBAAD;KAAxE,AAAA;IACA,MAAMyB,qBAAqB,GAAGH,QAAQ,CAACI,MAAT,GAAkB,CAAhD,AAAA;IAEA,MAAMC,qBAAqB,GAAG;QAC5BC,OAAO,EAAE3B,gBADmB;QAE5BqB,QAAQ,EAAEA,QAAQ,CAACO,MAAT,CAAgBC,+BAAhB,CAFkB;QAG5B,iFAAA;QACAC,WAAW,EAAEN,qBAAbM;KAJF,AAA8B;IAO9B,MAAM,E,WAAEC,SAAF,CAAA,E,UAAaC,QAAb,CAAA,E,UAAuBC,QAAvB,CAAA,E,GAAiCC,CAAjC,CAAA,E,GAAoCC,CAApC,CAAA,E,WAAuCC,SAAvC,CAAA,E,gBAAkDC,cAAlD,CAAA,E,QAAkEC,MAAAA,CAAAA,EAAlE,GAA6E3F,kBAAW,CAAC;QAC7F,gGAAA;QACAsF,QAAQ,EAAE,OAFmF;QAG7FG,SAAS,EAAEpB,gBAHkF;QAI7FuB,oBAAoB,EAAE3F,iBAJuE;QAK7F4F,UAAU,EAAE;YACVC,yCAAmB,EADT;YAEV5F,aAAM,CAAC;gBAAE6F,QAAQ,EAAE/C,UAAU,GAAGmB,WAAzB;gBAAsC6B,aAAa,EAAE9C,WAAf8C;aAAvC,CAFI;YAGVvC,eAAe,GACXtD,YAAK,CAAC;gBACJ4F,QAAQ,EAAE,IADN;gBAEJE,SAAS,EAAE,KAFP;gBAGJC,OAAO,EAAE3C,MAAM,KAAK,SAAX,GAAuBnD,iBAAU,EAAjC,GAAsC+F,SAH3C;gBAIJ,GAAGpB,qBAAH;aAJG,CADM,GAOXoB,SAVM;YAWV7F,KAAK,GAAGC,YAAe,CAAC;gBAAE6F,OAAO,EAAE9F,KAAX;gBAAkB0E,OAAO,EAAE7B,YAAT6B;aAAnB,CAAlB,GAAgEmB,SAX3D;YAYV1C,eAAe,GAAGjD,WAAI,CAAC;gBAAE,GAAGuE,qBADJ;aACF,CAAP,GAAwCoB,SAZ7C;YAaV1F,WAAI,CAAC;gBACH,GAAGsE,qBADA;gBAEHsB,KAAK,EAAE,CAAC,E,UAAEC,QAAF,CAAA,EAAYC,cAAc,EAAErC,KAA5B,CAAA,EAAmCsC,eAAe,EAAEpC,MAAjBoC,CAAAA,EAApC,GAAkE;oBACvEF,QAAQ,CAACjB,QAAT,CAAkBoB,KAAlB,CAAwBC,WAAxB,CAAoC,gCAApC,EAAuE,CAAA,EAAExC,KAAM,CAAA,EAAA,CAA/E,CAAAoC,CAAAA;oBACAA,QAAQ,CAACjB,QAAT,CAAkBoB,KAAlB,CAAwBC,WAAxB,CAAoC,iCAApC,EAAwE,CAAA,EAAEtC,MAAO,CAAA,EAAA,CAAjF,CAAAkC,CAAAA;iBACD;aALC,CAbM;YAoBVK,qCAAe,CAAC;gB,YAAE1C,UAAF;gB,aAAcE,WAAAA;aAAf,CApBL;YAqBVX,gBAAgB,GAAGnD,WAAI,CAAC;gBAAEiF,QAAQ,EAAE,iBAAVA;aAAH,CAAP,GAA2Ca,SArBjD;SAAA,CAsBVlB,MAtBU,CAsBH2B,+BAtBG,CAqBc;KA1BkE,CAA9F,AA5CwD,EA0ExD,4FA9B+F;IA+B/F7F,uBAAe,CAAC,IAAM;QACpBqE,SAAS,CAACpD,OAAO,CAACR,MAAT,CAAT,CAAA4D;KADa,EAEZ;QAACA,SAAD;QAAYpD,OAAO,CAACR,MAApB;KAFY,CAAf,CAEC;IAED,MAAMqF,QAAQ,GAAGtB,CAAC,KAAK,IAAN,IAAcC,CAAC,KAAK,IAArC,AAAA;IACA,MAAM,CAACsB,UAAD,EAAaC,WAAb,CAAA,GAA4BC,kDAA4B,CAACvB,SAAD,CAA9D,AAAA;IAEA,MAAMwB,YAAY,GAAGnG,qBAAc,CAAC4C,QAAD,CAAnC,AAAA;IACA3C,uBAAe,CAAC,IAAM;QACpB,IAAI8F,QAAJ,EACEI,YAAY,KAAA,IAAZ,IAAAA,YAAY,KAAA,KAAA,CAAZ,IAAAA,YAAY,EAAZA,CAAAA;KAFW,EAIZ;QAACJ,QAAD;QAAWI,YAAX;KAJY,CAAf,CAIC;IAED,MAAMC,MAAM,GAAA,AAAA,CAAA,qBAAA,GAAGxB,cAAc,CAACpF,KAAlB,CAAA,KAAA,IAAA,IAAA,qBAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAG,qBAAA,CAAsBiF,CAArC,AAAA;IACA,MAAM4B,MAAM,GAAA,AAAA,CAAA,sBAAA,GAAGzB,cAAc,CAACpF,KAAlB,CAAA,KAAA,IAAA,IAAA,sBAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAG,sBAAA,CAAsBkF,CAArC,AAAA;IACA,MAAM4B,iBAAiB,GAAG,AAAA,CAAA,AAAA,CAAA,sBAAA,GAAA1B,cAAc,CAACpF,KAAf,CAAA,KAAA,IAAA,IAAA,sBAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,sBAAA,CAAsB+G,YAAtB,CAAA,KAAuC,CAAjE,AAAA;IAEA,MAAM,CAACC,aAAD,EAAgBC,gBAAhB,CAAA,GAAoCxH,eAAA,EAA1C,AAAA;IACAgB,uBAAe,CAAC,IAAM;QACpB,IAAI6C,OAAJ,EAAa2D,gBAAgB,CAACC,MAAM,CAACC,gBAAP,CAAwB7D,OAAxB,CAAA,CAAiC8D,MAAlC,CAAhB,CAAb;KADa,EAEZ;QAAC9D,OAAD;KAFY,CAAf,CAEC;IAED,MAAM,E,WAAEhB,SAAF,CAAA,E,mBAAaC,iBAAAA,CAAAA,EAAb,GAAmCF,wCAAkB,CAACJ,kCAAD,EAAejB,aAAf,CAA3D,AAAM;IACN,MAAMqG,MAAM,GAAG,CAAC/E,SAAhB,AAAA;IAEA7C,sBAAA,CAAsB,IAAM;QAC1B,IAAI,CAAC4H,MAAL,EAAa;YACX9E,iBAAiB,CAAC+E,GAAlB,CAAsBjC,MAAtB,CAAA9C,CAAAA;YACA,OAAO,IAAM;gBACXA,iBAAiB,CAACgF,MAAlB,CAAyBlC,MAAzB,CAAA9C,CAAAA;aADF,CAEC;SACF;KANH,EAOG;QAAC8E,MAAD;QAAS9E,iBAAT;QAA4B8C,MAA5B;KAPH,CAAA,CArGwD,CA8GxD,uFAFC;IAGD,2DAAA;IACA,iFAAA;IACA,yDAAA;IACA5E,uBAAe,CAAC,IAAM;QACpB,IAAI4G,MAAM,IAAId,QAAd,EACElC,KAAK,CAACmD,IAAN,CAAWjF,iBAAX,CAAA,CACGkF,OADH,EAAA,CAEGC,OAFH,CAEYC,CAAAA,EAAD,GAAQC,qBAAqB,CAACD,EAAD,CAFxC;QAAA,CAAAtD,CAAAA;KAFW,EAMZ;QAACgD,MAAD;QAASd,QAAT;QAAmBhE,iBAAnB;KANY,CAAf,CAMC;IAED,MAAMsF,WAAW,GAAG;QAClB,WAAA,EAAarB,UADK;QAElB,YAAA,EAAcC,WAFI;QAGlB,GAAGpD,YAHe;QAIlB1B,GAAG,EAAEE,YAJa;QAKlBsE,KAAK,EAAE;YACL,GAAG9C,YAAY,CAAC8C,KADX;YAEL,0EAAA;YACA,gGAAA;YACA2B,SAAS,EAAE,CAACvB,QAAD,GAAY,MAAZ,GAAqBV,SAJ3B;YAKL,qEAAA;YACAkC,OAAO,EAAE,AAAA,CAAA,oBAAA,GAAA3C,cAAc,CAACrF,IAAf,CAAA,KAAA,IAAA,IAAA,oBAAA,KAAA,KAAA,CAAA,IAAA,oBAAA,CAAqBiI,eAArB,GAAuC,CAAvC,GAA2CnC,SAApDkC;SANK;KALT,AAAoB;IAepB,OAAA,aACE,CAAA,oBADF,CAAA,KAAA,EAAA;QAEI,GAAG,EAAEhD,QADP;QAEE,mCAAA,EAAkC,EAFpC;QAGE,KAAK,EAAE;YACLkD,QAAQ,EAAEjD,QADL;YAELb,IAAI,EAAE,CAFD;YAGLH,GAAG,EAAE,CAHA;YAILkE,SAAS,EAAE3B,QAAQ,GACd,CAAA,YAAA,EAAc4B,IAAI,CAACC,KAAL,CAAWnD,CAAX,CAAc,CAAA,IAAA,EAAMkD,IAAI,CAACC,KAAL,CAAWlD,CAAX,CAAc,CAAA,MAAA,CADlC,GAEf,0BANC;YAM2B,mCAAA;YAChCmD,QAAQ,EAAE,aAPL;YAQLjB,MAAM,EAAEJ,aARH;YASL,CAAC,iCAAD,CAAA,EAA4C;gBAAA,CAAA,qBAAA,GAC1C5B,cAAc,CAACiB,eAD2B,CAAA,KAAA,IAAA,IAAA,qBAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAC1C,qBAAA,CAAgCpB,CADU;gBAAA,CAAA,sBAAA,GAE1CG,cAAc,CAACiB,eAF2B,CAAA,KAAA,IAAA,IAAA,sBAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAE1C,sBAAA,CAAgCnB,CAFU;aAAA,CAG1CoD,IAH0C,CAGrC,GAHqC,CAA5C;SAZJ,CAiBE,kFAdO;QAHT;QAoBE,GAAG,EAAEvH,KAAK,CAACwH,GAAX;KApBF,EAAA,aAsBE,CAAA,oBAAA,CAAC,2CAAD,EAtBF;QAuBI,KAAK,EAAEvH,aADT;QAEE,UAAU,EAAEwF,UAFd;QAGE,aAAa,EAAE/C,QAHjB;QAIE,MAAM,EAAEmD,MAJV;QAKE,MAAM,EAAEC,MALV;QAME,eAAe,EAAEC,iBAAjB;KANF,EAQGO,MAAM,GAAA,aACL,CAAA,oBAAA,CAAC,6CAAD,EATJ;QAUM,KAAK,EAAErG,aADT;QAEE,SAAS,EAAA,IAFX;QAGE,iBAAiB,EAAEuB,iBAAnB;KAHF,EAAA,aAKE,CAAA,oBAAA,CAAC,gBAAD,CAAW,GAAX,EAAmBsF,WAAnB,CALF,CADK,GAAA,aASL,CAAA,oBAAA,CAAC,gBAAD,CAAW,GAAX,EAAmBA,WAAnB,CAjBJ,CAtBF,CADF,CAgCQ;CA1KU,CAAtB,AAuLG;AAGH,aAAA,CAAA,MAAA,CAAA,MAAA,CAAA,wCAAA,EAAA;IAAA,WAAA,EAAA,kCAAA;CAAA,CAAA,CAAA;AAEA;;oGAEA,CAEA,MAAMW,gCAAU,GAAG,aAAnB,AAAA;AAEA,MAAMC,mCAAiC,GAAG;IACxCzE,GAAG,EAAE,QADmC;IAExCC,KAAK,EAAE,MAFiC;IAGxCC,MAAM,EAAE,KAHgC;IAIxCC,IAAI,EAAE,OAANA;CAJF,AAA0C;AAW1C,MAAMjF,yCAAW,GAAA,aAAGO,CAAAA,iBAAA,CAAuD,SAASP,yCAAT,CACzE6B,KADyE,EAEzEQ,YAFyE,EAGzE;IACA,MAAM,E,eAAEP,aAAF,CAAA,EAAiB,GAAG0H,UAAH,EAAjB,GAAmC3H,KAAzC,AAAM;IACN,MAAM4H,cAAc,GAAGxG,uCAAiB,CAACqG,gCAAD,EAAaxH,aAAb,CAAxC,AAAA;IACA,MAAM4H,QAAQ,GAAGH,mCAAa,CAACE,cAAc,CAACnC,UAAhB,CAA9B,AAAA;IAEA,OAAA,aAAA,CACE,+EAAA;IACA,sDAAA;IACA,sFAAA;IACA,oBAAA,CAAA,MAAA,EAAA;QACE,GAAG,EAAEmC,cAAc,CAACE,aADtB;QAEE,KAAK,EAAE;YACLZ,QAAQ,EAAE,UADL;YAEL9D,IAAI,EAAEwE,cAAc,CAAC/B,MAFhB;YAGL5C,GAAG,EAAE2E,cAAc,CAAC9B,MAHf;YAIL,CAAC+B,QAAD,CAAA,EAAY,CAJP;YAKLvC,eAAe,EAAE;gBACfrC,GAAG,EAAE,EADU;gBAEfC,KAAK,EAAE,KAFQ;gBAGfC,MAAM,EAAE,UAHO;gBAIfC,IAAI,EAAE,QAANA;aAJe,CAKfwE,cAAc,CAACnC,UALA,CALZ;YAWL0B,SAAS,EAAE;gBACTlE,GAAG,EAAE,kBADI;gBAETC,KAAK,EAAE,gDAFE;gBAGTC,MAAM,EAAG,CAAA,cAAA,CAHA;gBAITC,IAAI,EAAE,gDAANA;aAJS,CAKTwE,cAAc,CAACnC,UALN,CAXN;YAiBLsC,UAAU,EAAEH,cAAc,CAACI,eAAf,GAAiC,QAAjC,GAA4ClD,SAAxDiD;SAjBK;KAFT,EAAA,aAsBE,CAAA,oBAAA,CAAC,WAAD,EAAA,oCAAA,CAAA,EAAA,EACMJ,UADN,EAtBF;QAwBI,GAAG,EAAEnH,YAFP;QAGE,KAAK,EAAE;YACL,GAAGmH,UAAU,CAACvC,KADT;YAEL,oEAAA;YACA6C,OAAO,EAAE,OAATA;SAHK;KAHT,CAAA,CAtBF,CAsBE,EA1BJ;CARkB,CAApB,AA6CC;AAED,aAAA,CAAA,MAAA,CAAA,MAAA,CAAA,yCAAA,EAAA;IAAA,WAAA,EAAA,gCAAA;CAAA,CAAA,CAAA;AAEA,oGAAA,CAEA,SAAS1C,+BAAT,CAAsB2C,KAAtB,EAAwD;IACtD,OAAOA,KAAK,KAAKpD,SAAjB,CAAA;CACD;AAED,SAASjB,+BAAT,CAAsBqE,KAAtB,EAAmD;IACjD,OAAOA,KAAK,KAAK,IAAjB,CAAA;CACD;AAED,MAAMzD,yCAAmB,GAAG,IAAmB,CAAA;QAC7C0D,IAAI,EAAE,qBADuC;QAE7CvB,EAAE,EAACwB,IAAD,EAAO;YACP,MAAM,E,OAAEC,KAAF,CAAA,E,UAASpD,QAAAA,CAAAA,EAAT,GAAsBmD,IAA5B,AAAM;YACN,MAAM,E,OAAEvF,KAAF,CAAA,E,QAASE,MAAAA,CAAAA,EAAT,GAAoBsF,KAAK,CAACtE,SAAhC,AAAM;YACNkB,QAAQ,CAACjB,QAAT,CAAkBoB,KAAlB,CAAwBC,WAAxB,CAAoC,6BAApC,EAAoE,CAAA,EAAExC,KAAM,CAAA,EAAA,CAA5E,CAAAoC,CAAAA;YACAA,QAAQ,CAACjB,QAAT,CAAkBoB,KAAlB,CAAwBC,WAAxB,CAAoC,8BAApC,EAAqE,CAAA,EAAEtC,MAAO,CAAA,EAAA,CAA9E,CAAAkC,CAAAA;YACA,OAAO,EAAP,CAAA;SACD;KARyB,CAAA;AAAmB;AAW/C,MAAMK,qCAAe,GAAIgD,CAAAA,OAAD,GAAuE,CAAA;QAC7FH,IAAI,EAAE,iBADuF;Q,SAE7FG,OAF6F;QAG7F1B,EAAE,EAACwB,IAAD,EAAO;YAAA,IAAA,sBAAA,EAAA,sBAAA,EAAA,sBAAA,EAAA,sBAAA,EAAA,sBAAA,AAAA;YACP,MAAM,E,WAAEhE,SAAF,CAAA,E,OAAaiE,KAAb,CAAA,E,gBAAoBhE,cAAAA,CAAAA,EAApB,GAAuC+D,IAA7C,AAAM;YAEN,MAAMrC,iBAAiB,GAAG,AAAA,CAAA,AAAA,CAAA,sBAAA,GAAA1B,cAAc,CAACpF,KAAf,CAAA,KAAA,IAAA,IAAA,sBAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,sBAAA,CAAsB+G,YAAtB,CAAA,KAAuC,CAAjE,AAAA;YACA,MAAMuC,aAAa,GAAGxC,iBAAtB,AAAA;YACA,MAAMnD,UAAU,GAAG2F,aAAa,GAAG,CAAH,GAAOD,OAAO,CAAC1F,UAA/C,AAAA;YACA,MAAME,WAAW,GAAGyF,aAAa,GAAG,CAAH,GAAOD,OAAO,CAACxF,WAAhD,AAAA;YAEA,MAAM,CAAC2C,UAAD,EAAaC,WAAb,CAAA,GAA4BC,kDAA4B,CAACvB,SAAD,CAA9D,AAAA;YACA,MAAMoE,YAAY,GAAG;gBAAEC,KAAK,EAAE,IAAT;gBAAeC,MAAM,EAAE,KAAvB;gBAA8BC,GAAG,EAAE,MAALA;aAA9B,CAA4CjD,WAA5C,CAArB,AAAqB;YAErB,MAAMkD,YAAY,GAAG,AAAA,CAAA,AAAA,CAAA,sBAAA,GAAA,AAAA,CAAA,sBAAA,GAACvE,cAAc,CAACpF,KAAhB,CAAA,KAAA,IAAA,IAAA,sBAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAC,sBAAA,CAAsBiF,CAAvB,CAAA,KAAA,IAAA,IAAA,sBAAA,KAAA,KAAA,CAAA,GAAA,sBAAA,GAA4B,CAA5B,CAAA,GAAiCtB,UAAU,GAAG,CAAnE,AAAA;YACA,MAAMiG,YAAY,GAAG,AAAA,CAAA,AAAA,CAAA,sBAAA,GAAA,AAAA,CAAA,sBAAA,GAACxE,cAAc,CAACpF,KAAhB,CAAA,KAAA,IAAA,IAAA,sBAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAC,sBAAA,CAAsBkF,CAAvB,CAAA,KAAA,IAAA,IAAA,sBAAA,KAAA,KAAA,CAAA,GAAA,sBAAA,GAA4B,CAA5B,CAAA,GAAiCrB,WAAW,GAAG,CAApE,AAAA;YAEA,IAAIoB,CAAC,GAAG,EAAR,AAAA;YACA,IAAIC,CAAC,GAAG,EAAR,AAAA;YAEA,IAAIsB,UAAU,KAAK,QAAnB,EAA6B;gBAC3BvB,CAAC,GAAGqE,aAAa,GAAGC,YAAH,GAAmB,CAAA,EAAEI,YAAa,CAAA,EAAA,CAAnD,CAAA1E;gBACAC,CAAC,GAAI,CAAA,EAAE,CAACrB,WAAY,CAAA,EAAA,CAApB,CAAAqB;aAFF,MAGO,IAAIsB,UAAU,KAAK,KAAnB,EAA0B;gBAC/BvB,CAAC,GAAGqE,aAAa,GAAGC,YAAH,GAAmB,CAAA,EAAEI,YAAa,CAAA,EAAA,CAAnD,CAAA1E;gBACAC,CAAC,GAAI,CAAA,EAAEkE,KAAK,CAACrE,QAAN,CAAejB,MAAf,GAAwBD,WAAY,CAAA,EAAA,CAA3C,CAAAqB;aAFK,MAGA,IAAIsB,UAAU,KAAK,OAAnB,EAA4B;gBACjCvB,CAAC,GAAI,CAAA,EAAE,CAACpB,WAAY,CAAA,EAAA,CAApB,CAAAoB;gBACAC,CAAC,GAAGoE,aAAa,GAAGC,YAAH,GAAmB,CAAA,EAAEK,YAAa,CAAA,EAAA,CAAnD,CAAA1E;aAFK,MAGA,IAAIsB,UAAU,KAAK,MAAnB,EAA2B;gBAChCvB,CAAC,GAAI,CAAA,EAAEmE,KAAK,CAACrE,QAAN,CAAenB,KAAf,GAAuBC,WAAY,CAAA,EAAA,CAA1C,CAAAoB;gBACAC,CAAC,GAAGoE,aAAa,GAAGC,YAAH,GAAmB,CAAA,EAAEK,YAAa,CAAA,EAAA,CAAnD,CAAA1E;aACD;YACD,OAAO;gBAAEiE,IAAI,EAAE;oB,GAAElE,CAAF;oB,GAAKC,CAAAA;iBAAL;aAAf,CAAO;SACR;KAlCqB,CAAA;AAAuE;AAqC/F,SAASwB,kDAAT,CAAsCvB,SAAtC,EAA4D;IAC1D,MAAM,CAAC1C,IAAD,EAAOE,KAAK,GAAG,QAAf,CAAA,GAA2BwC,SAAS,CAAC0E,KAAV,CAAgB,GAAhB,CAAjC,AAAA;IACA,OAAO;QAACpH,IAAD;QAAeE,KAAf;KAAP,CAAA;CACD;AAED,MAAMxD,yCAAI,GAAGJ,yCAAb,AAAA;AACA,MAAMK,yCAAM,GAAGJ,wCAAf,AAAA;AACA,MAAMK,yCAAO,GAAGJ,wCAAhB,AAAA;AACA,MAAMK,yCAAK,GAAGJ,yCAAd,AAAA;;ADpcA", "sources": ["packages/react/popper/src/index.ts", "packages/react/popper/src/Popper.tsx"], "sourcesContent": ["export {\n  createPopperScope,\n  //\n  <PERSON><PERSON>,\n  <PERSON>perAnchor,\n  PopperContent,\n  PopperArrow,\n  //\n  Root,\n  Anchor,\n  Content,\n  Arrow,\n  //\n  SIDE_OPTIONS,\n  ALIGN_OPTIONS,\n} from './Popper';\nexport type {\n  PopperProps,\n  PopperAnchorProps,\n  PopperContentProps,\n  PopperArrowProps,\n} from './Popper';\n", "import * as React from 'react';\nimport {\n  useFloating,\n  autoUpdate,\n  offset,\n  shift,\n  limitShift,\n  hide,\n  arrow as floatingUIarrow,\n  flip,\n  size,\n} from '@floating-ui/react-dom';\nimport * as ArrowPrimitive from '@radix-ui/react-arrow';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport { useCallbackRef } from '@radix-ui/react-use-callback-ref';\nimport { useLayoutEffect } from '@radix-ui/react-use-layout-effect';\nimport { useSize } from '@radix-ui/react-use-size';\n\nimport type { Placement, Middleware } from '@floating-ui/react-dom';\nimport type * as Radix from '@radix-ui/react-primitive';\nimport type { Scope } from '@radix-ui/react-context';\nimport type { Measurable } from '@radix-ui/rect';\n\nconst SIDE_OPTIONS = ['top', 'right', 'bottom', 'left'] as const;\nconst ALIGN_OPTIONS = ['start', 'center', 'end'] as const;\n\ntype Side = typeof SIDE_OPTIONS[number];\ntype Align = typeof ALIGN_OPTIONS[number];\n\n/* -------------------------------------------------------------------------------------------------\n * Popper\n * -----------------------------------------------------------------------------------------------*/\n\nconst POPPER_NAME = 'Popper';\n\ntype ScopedProps<P> = P & { __scopePopper?: Scope };\nconst [createPopperContext, createPopperScope] = createContextScope(POPPER_NAME);\n\ntype PopperContextValue = {\n  anchor: Measurable | null;\n  onAnchorChange(anchor: Measurable | null): void;\n};\nconst [PopperProvider, usePopperContext] = createPopperContext<PopperContextValue>(POPPER_NAME);\n\ninterface PopperProps {\n  children?: React.ReactNode;\n}\nconst Popper: React.FC<PopperProps> = (props: ScopedProps<PopperProps>) => {\n  const { __scopePopper, children } = props;\n  const [anchor, setAnchor] = React.useState<Measurable | null>(null);\n  return (\n    <PopperProvider scope={__scopePopper} anchor={anchor} onAnchorChange={setAnchor}>\n      {children}\n    </PopperProvider>\n  );\n};\n\nPopper.displayName = POPPER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * PopperAnchor\n * -----------------------------------------------------------------------------------------------*/\n\nconst ANCHOR_NAME = 'PopperAnchor';\n\ntype PopperAnchorElement = React.ElementRef<typeof Primitive.div>;\ntype PrimitiveDivProps = Radix.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface PopperAnchorProps extends PrimitiveDivProps {\n  virtualRef?: React.RefObject<Measurable>;\n}\n\nconst PopperAnchor = React.forwardRef<PopperAnchorElement, PopperAnchorProps>(\n  (props: ScopedProps<PopperAnchorProps>, forwardedRef) => {\n    const { __scopePopper, virtualRef, ...anchorProps } = props;\n    const context = usePopperContext(ANCHOR_NAME, __scopePopper);\n    const ref = React.useRef<PopperAnchorElement>(null);\n    const composedRefs = useComposedRefs(forwardedRef, ref);\n\n    React.useEffect(() => {\n      // Consumer can anchor the popper to something that isn't\n      // a DOM node e.g. pointer position, so we override the\n      // `anchorRef` with their virtual ref in this case.\n      context.onAnchorChange(virtualRef?.current || ref.current);\n    });\n\n    return virtualRef ? null : <Primitive.div {...anchorProps} ref={composedRefs} />;\n  }\n);\n\nPopperAnchor.displayName = ANCHOR_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * PopperContent\n * -----------------------------------------------------------------------------------------------*/\n\nconst CONTENT_NAME = 'PopperContent';\n\ntype PopperContentContextValue = {\n  placedSide: Side;\n  onArrowChange(arrow: HTMLSpanElement | null): void;\n  arrowX?: number;\n  arrowY?: number;\n  shouldHideArrow: boolean;\n};\n\nconst [PopperContentProvider, useContentContext] =\n  createPopperContext<PopperContentContextValue>(CONTENT_NAME);\n\nconst [PositionContextProvider, usePositionContext] = createPopperContext(CONTENT_NAME, {\n  hasParent: false,\n  positionUpdateFns: new Set<() => void>(),\n});\n\ntype Boundary = Element | null;\n\ntype PopperContentElement = React.ElementRef<typeof Primitive.div>;\ninterface PopperContentProps extends PrimitiveDivProps {\n  side?: Side;\n  sideOffset?: number;\n  align?: Align;\n  alignOffset?: number;\n  arrowPadding?: number;\n  collisionBoundary?: Boundary | Boundary[];\n  collisionPadding?: number | Partial<Record<Side, number>>;\n  sticky?: 'partial' | 'always';\n  hideWhenDetached?: boolean;\n  avoidCollisions?: boolean;\n  onPlaced?: () => void;\n}\n\nconst PopperContent = React.forwardRef<PopperContentElement, PopperContentProps>(\n  (props: ScopedProps<PopperContentProps>, forwardedRef) => {\n    const {\n      __scopePopper,\n      side = 'bottom',\n      sideOffset = 0,\n      align = 'center',\n      alignOffset = 0,\n      arrowPadding = 0,\n      collisionBoundary = [],\n      collisionPadding: collisionPaddingProp = 0,\n      sticky = 'partial',\n      hideWhenDetached = false,\n      avoidCollisions = true,\n      onPlaced,\n      ...contentProps\n    } = props;\n\n    const context = usePopperContext(CONTENT_NAME, __scopePopper);\n\n    const [content, setContent] = React.useState<HTMLDivElement | null>(null);\n    const composedRefs = useComposedRefs(forwardedRef, (node) => setContent(node));\n\n    const [arrow, setArrow] = React.useState<HTMLSpanElement | null>(null);\n    const arrowSize = useSize(arrow);\n    const arrowWidth = arrowSize?.width ?? 0;\n    const arrowHeight = arrowSize?.height ?? 0;\n\n    const desiredPlacement = (side + (align !== 'center' ? '-' + align : '')) as Placement;\n\n    const collisionPadding =\n      typeof collisionPaddingProp === 'number'\n        ? collisionPaddingProp\n        : { top: 0, right: 0, bottom: 0, left: 0, ...collisionPaddingProp };\n\n    const boundary = Array.isArray(collisionBoundary) ? collisionBoundary : [collisionBoundary];\n    const hasExplicitBoundaries = boundary.length > 0;\n\n    const detectOverflowOptions = {\n      padding: collisionPadding,\n      boundary: boundary.filter(isNotNull),\n      // with `strategy: 'fixed'`, this is the only way to get it to respect boundaries\n      altBoundary: hasExplicitBoundaries,\n    };\n\n    const { reference, floating, strategy, x, y, placement, middlewareData, update } = useFloating({\n      // default to `fixed` strategy so users don't have to pick and we also avoid focus scroll issues\n      strategy: 'fixed',\n      placement: desiredPlacement,\n      whileElementsMounted: autoUpdate,\n      middleware: [\n        anchorCssProperties(),\n        offset({ mainAxis: sideOffset + arrowHeight, alignmentAxis: alignOffset }),\n        avoidCollisions\n          ? shift({\n              mainAxis: true,\n              crossAxis: false,\n              limiter: sticky === 'partial' ? limitShift() : undefined,\n              ...detectOverflowOptions,\n            })\n          : undefined,\n        arrow ? floatingUIarrow({ element: arrow, padding: arrowPadding }) : undefined,\n        avoidCollisions ? flip({ ...detectOverflowOptions }) : undefined,\n        size({\n          ...detectOverflowOptions,\n          apply: ({ elements, availableWidth: width, availableHeight: height }) => {\n            elements.floating.style.setProperty('--radix-popper-available-width', `${width}px`);\n            elements.floating.style.setProperty('--radix-popper-available-height', `${height}px`);\n          },\n        }),\n        transformOrigin({ arrowWidth, arrowHeight }),\n        hideWhenDetached ? hide({ strategy: 'referenceHidden' }) : undefined,\n      ].filter(isDefined),\n    });\n\n    // assign the reference dynamically once `Content` has mounted so we can collocate the logic\n    useLayoutEffect(() => {\n      reference(context.anchor);\n    }, [reference, context.anchor]);\n\n    const isPlaced = x !== null && y !== null;\n    const [placedSide, placedAlign] = getSideAndAlignFromPlacement(placement);\n\n    const handlePlaced = useCallbackRef(onPlaced);\n    useLayoutEffect(() => {\n      if (isPlaced) {\n        handlePlaced?.();\n      }\n    }, [isPlaced, handlePlaced]);\n\n    const arrowX = middlewareData.arrow?.x;\n    const arrowY = middlewareData.arrow?.y;\n    const cannotCenterArrow = middlewareData.arrow?.centerOffset !== 0;\n\n    const [contentZIndex, setContentZIndex] = React.useState<string>();\n    useLayoutEffect(() => {\n      if (content) setContentZIndex(window.getComputedStyle(content).zIndex);\n    }, [content]);\n\n    const { hasParent, positionUpdateFns } = usePositionContext(CONTENT_NAME, __scopePopper);\n    const isRoot = !hasParent;\n\n    React.useLayoutEffect(() => {\n      if (!isRoot) {\n        positionUpdateFns.add(update);\n        return () => {\n          positionUpdateFns.delete(update);\n        };\n      }\n    }, [isRoot, positionUpdateFns, update]);\n\n    // when nested contents are rendered in portals, they are appended out of order causing\n    // children to be positioned incorrectly if initially open.\n    // we need to re-compute the positioning once the parent has finally been placed.\n    // https://github.com/floating-ui/floating-ui/issues/1531\n    useLayoutEffect(() => {\n      if (isRoot && isPlaced) {\n        Array.from(positionUpdateFns)\n          .reverse()\n          .forEach((fn) => requestAnimationFrame(fn));\n      }\n    }, [isRoot, isPlaced, positionUpdateFns]);\n\n    const commonProps = {\n      'data-side': placedSide,\n      'data-align': placedAlign,\n      ...contentProps,\n      ref: composedRefs,\n      style: {\n        ...contentProps.style,\n        // if the PopperContent hasn't been placed yet (not all measurements done)\n        // we prevent animations so that users's animation don't kick in too early referring wrong sides\n        animation: !isPlaced ? 'none' : undefined,\n        // hide the content if using the hide middleware and should be hidden\n        opacity: middlewareData.hide?.referenceHidden ? 0 : undefined,\n      },\n    };\n\n    return (\n      <div\n        ref={floating}\n        data-radix-popper-content-wrapper=\"\"\n        style={{\n          position: strategy,\n          left: 0,\n          top: 0,\n          transform: isPlaced\n            ? `translate3d(${Math.round(x)}px, ${Math.round(y)}px, 0)`\n            : 'translate3d(0, -200%, 0)', // keep off the page when measuring\n          minWidth: 'max-content',\n          zIndex: contentZIndex,\n          ['--radix-popper-transform-origin' as any]: [\n            middlewareData.transformOrigin?.x,\n            middlewareData.transformOrigin?.y,\n          ].join(' '),\n        }}\n        // Floating UI interally calculates logical alignment based the `dir` attribute on\n        // the reference/floating node, we must add this attribute here to ensure\n        // this is calculated when portalled as well as inline.\n        dir={props.dir}\n      >\n        <PopperContentProvider\n          scope={__scopePopper}\n          placedSide={placedSide}\n          onArrowChange={setArrow}\n          arrowX={arrowX}\n          arrowY={arrowY}\n          shouldHideArrow={cannotCenterArrow}\n        >\n          {isRoot ? (\n            <PositionContextProvider\n              scope={__scopePopper}\n              hasParent\n              positionUpdateFns={positionUpdateFns}\n            >\n              <Primitive.div {...commonProps} />\n            </PositionContextProvider>\n          ) : (\n            <Primitive.div {...commonProps} />\n          )}\n        </PopperContentProvider>\n      </div>\n    );\n  }\n);\n\nPopperContent.displayName = CONTENT_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * PopperArrow\n * -----------------------------------------------------------------------------------------------*/\n\nconst ARROW_NAME = 'PopperArrow';\n\nconst OPPOSITE_SIDE: Record<Side, Side> = {\n  top: 'bottom',\n  right: 'left',\n  bottom: 'top',\n  left: 'right',\n};\n\ntype PopperArrowElement = React.ElementRef<typeof ArrowPrimitive.Root>;\ntype ArrowProps = Radix.ComponentPropsWithoutRef<typeof ArrowPrimitive.Root>;\ninterface PopperArrowProps extends ArrowProps {}\n\nconst PopperArrow = React.forwardRef<PopperArrowElement, PopperArrowProps>(function PopperArrow(\n  props: ScopedProps<PopperArrowProps>,\n  forwardedRef\n) {\n  const { __scopePopper, ...arrowProps } = props;\n  const contentContext = useContentContext(ARROW_NAME, __scopePopper);\n  const baseSide = OPPOSITE_SIDE[contentContext.placedSide];\n\n  return (\n    // we have to use an extra wrapper because `ResizeObserver` (used by `useSize`)\n    // doesn't report size as we'd expect on SVG elements.\n    // it reports their bounding box which is effectively the largest path inside the SVG.\n    <span\n      ref={contentContext.onArrowChange}\n      style={{\n        position: 'absolute',\n        left: contentContext.arrowX,\n        top: contentContext.arrowY,\n        [baseSide]: 0,\n        transformOrigin: {\n          top: '',\n          right: '0 0',\n          bottom: 'center 0',\n          left: '100% 0',\n        }[contentContext.placedSide],\n        transform: {\n          top: 'translateY(100%)',\n          right: 'translateY(50%) rotate(90deg) translateX(-50%)',\n          bottom: `rotate(180deg)`,\n          left: 'translateY(50%) rotate(-90deg) translateX(50%)',\n        }[contentContext.placedSide],\n        visibility: contentContext.shouldHideArrow ? 'hidden' : undefined,\n      }}\n    >\n      <ArrowPrimitive.Root\n        {...arrowProps}\n        ref={forwardedRef}\n        style={{\n          ...arrowProps.style,\n          // ensures the element can be measured correctly (mostly for if SVG)\n          display: 'block',\n        }}\n      />\n    </span>\n  );\n});\n\nPopperArrow.displayName = ARROW_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nfunction isDefined<T>(value: T | undefined): value is T {\n  return value !== undefined;\n}\n\nfunction isNotNull<T>(value: T | null): value is T {\n  return value !== null;\n}\n\nconst anchorCssProperties = (): Middleware => ({\n  name: 'anchorCssProperties',\n  fn(data) {\n    const { rects, elements } = data;\n    const { width, height } = rects.reference;\n    elements.floating.style.setProperty('--radix-popper-anchor-width', `${width}px`);\n    elements.floating.style.setProperty('--radix-popper-anchor-height', `${height}px`);\n    return {};\n  },\n});\n\nconst transformOrigin = (options: { arrowWidth: number; arrowHeight: number }): Middleware => ({\n  name: 'transformOrigin',\n  options,\n  fn(data) {\n    const { placement, rects, middlewareData } = data;\n\n    const cannotCenterArrow = middlewareData.arrow?.centerOffset !== 0;\n    const isArrowHidden = cannotCenterArrow;\n    const arrowWidth = isArrowHidden ? 0 : options.arrowWidth;\n    const arrowHeight = isArrowHidden ? 0 : options.arrowHeight;\n\n    const [placedSide, placedAlign] = getSideAndAlignFromPlacement(placement);\n    const noArrowAlign = { start: '0%', center: '50%', end: '100%' }[placedAlign];\n\n    const arrowXCenter = (middlewareData.arrow?.x ?? 0) + arrowWidth / 2;\n    const arrowYCenter = (middlewareData.arrow?.y ?? 0) + arrowHeight / 2;\n\n    let x = '';\n    let y = '';\n\n    if (placedSide === 'bottom') {\n      x = isArrowHidden ? noArrowAlign : `${arrowXCenter}px`;\n      y = `${-arrowHeight}px`;\n    } else if (placedSide === 'top') {\n      x = isArrowHidden ? noArrowAlign : `${arrowXCenter}px`;\n      y = `${rects.floating.height + arrowHeight}px`;\n    } else if (placedSide === 'right') {\n      x = `${-arrowHeight}px`;\n      y = isArrowHidden ? noArrowAlign : `${arrowYCenter}px`;\n    } else if (placedSide === 'left') {\n      x = `${rects.floating.width + arrowHeight}px`;\n      y = isArrowHidden ? noArrowAlign : `${arrowYCenter}px`;\n    }\n    return { data: { x, y } };\n  },\n});\n\nfunction getSideAndAlignFromPlacement(placement: Placement) {\n  const [side, align = 'center'] = placement.split('-');\n  return [side as Side, align as Align] as const;\n}\n\nconst Root = Popper;\nconst Anchor = PopperAnchor;\nconst Content = PopperContent;\nconst Arrow = PopperArrow;\n\nexport {\n  createPopperScope,\n  //\n  Popper,\n  PopperAnchor,\n  PopperContent,\n  PopperArrow,\n  //\n  Root,\n  Anchor,\n  Content,\n  Arrow,\n  //\n  SIDE_OPTIONS,\n  ALIGN_OPTIONS,\n};\nexport type { PopperProps, PopperAnchorProps, PopperContentProps, PopperArrowProps };\n"], "names": ["createPopperScope", "<PERSON><PERSON>", "PopperA<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PopperArrow", "Root", "<PERSON><PERSON>", "Content", "Arrow", "SIDE_OPTIONS", "ALIGN_OPTIONS", "React", "useFloating", "autoUpdate", "offset", "shift", "limitShift", "hide", "arrow", "floatingUIarrow", "flip", "size", "ArrowPrimitive", "useComposedRefs", "createContextScope", "Primitive", "useCallbackRef", "useLayoutEffect", "useSize", "POPPER_NAME", "createPopperContext", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "usePopperContext", "props", "__scope<PERSON>opper", "children", "anchor", "setAnchor", "useState", "ANCHOR_NAME", "forwardRef", "forwardedRef", "virtualRef", "anchorProps", "context", "ref", "useRef", "composedRefs", "useEffect", "onAnchorChange", "current", "CONTENT_NAME", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "useContentContext", "PositionContextProvider", "usePositionContext", "hasParent", "positionUpdateFns", "Set", "side", "sideOffset", "align", "alignOffset", "arrowPadding", "collisionBoundary", "collisionPadding", "collisionPaddingProp", "sticky", "hideWhenDetached", "avoidCollisions", "onPlaced", "contentProps", "content", "<PERSON><PERSON><PERSON><PERSON>", "node", "setArrow", "arrowSize", "arrow<PERSON>idth", "width", "arrowHeight", "height", "desiredPlacement", "top", "right", "bottom", "left", "boundary", "Array", "isArray", "hasExplicitBoundaries", "length", "detectOverflowOptions", "padding", "filter", "isNotNull", "altBoundary", "reference", "floating", "strategy", "x", "y", "placement", "middlewareData", "update", "whileElementsMounted", "middleware", "anchorCssProperties", "mainAxis", "alignmentAxis", "crossAxis", "limiter", "undefined", "element", "apply", "elements", "availableWidth", "availableHeight", "style", "setProperty", "transform<PERSON><PERSON>in", "isDefined", "isPlaced", "placedSide", "placedAlign", "getSideAndAlignFromPlacement", "handlePlaced", "arrowX", "arrowY", "cannotCenterArrow", "centerOffset", "contentZIndex", "setContentZIndex", "window", "getComputedStyle", "zIndex", "isRoot", "add", "delete", "from", "reverse", "for<PERSON>ach", "fn", "requestAnimationFrame", "commonProps", "animation", "opacity", "referenceHidden", "position", "transform", "Math", "round", "min<PERSON><PERSON><PERSON>", "join", "dir", "ARROW_NAME", "OPPOSITE_SIDE", "arrowProps", "contentContext", "baseSide", "onArrowChange", "visibility", "shouldHideArrow", "display", "value", "name", "data", "rects", "options", "isArrowHidden", "noArrowAlign", "start", "center", "end", "arrowXCenter", "arrowYCenter", "split"], "version": 3, "file": "index.module.js.map"}