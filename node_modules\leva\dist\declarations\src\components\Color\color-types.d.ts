import type { ColorVectorInput, InputWithSettings, LevaInputProps } from '../../types';
export declare type Format = 'hex' | 'rgb' | 'hsl' | 'hsv';
export declare type Color = string | ColorVectorInput;
export declare type InternalColorSettings = {
    format: Format;
    hasAlpha: boolean;
    isString: boolean;
};
export declare type ColorInput = InputWithSettings<Color>;
export declare type ColorProps = LevaInputProps<Color, InternalColorSettings, string>;
