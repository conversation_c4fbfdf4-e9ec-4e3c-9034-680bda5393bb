<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Portfolio Test</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #0a0a0a;
            color: #ffffff;
            font-family: 'Courier New', monospace;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            text-align: center;
        }
        h1 {
            color: #00ff00;
            font-size: 2rem;
            margin-bottom: 20px;
        }
        p {
            margin: 10px 0;
            font-size: 1.1rem;
        }
        .success { color: #00ffff; }
        .warning { color: #ffff00; }
        .info { color: #ffffff; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Terminal Portfolio Test</h1>
        <p class="success">✅ HTML is loading correctly!</p>
        <p class="success">✅ CSS styles are working!</p>
        <p class="warning">⚠️ If you can see this, the server is working</p>
        <p class="info">Now we need to check why React isn't loading...</p>
        
        <div style="margin-top: 30px; padding: 20px; border: 1px solid #00ffff; border-radius: 10px;">
            <h3 style="color: #00ffff;">Debug Information:</h3>
            <p class="info">Server: Running on port 3000</p>
            <p class="info">Static files: Working</p>
            <p class="warning">React app: Needs investigation</p>
        </div>
    </div>
    
    <script>
        console.log('Static HTML test page loaded successfully!');
        console.log('If you can see this in the console, JavaScript is working.');
    </script>
</body>
</html>
