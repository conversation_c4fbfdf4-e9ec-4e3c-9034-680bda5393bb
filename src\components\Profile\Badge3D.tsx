import React, { useRef, useState } from 'react';
import { Canvas, useFrame, useThree } from '@react-three/fiber';
import { Text, RoundedBox, Float } from '@react-three/drei';
import * as THREE from 'three';
import styled from 'styled-components';
import { personalInfo, contactInfo } from '../../data/portfolio';

const CanvasContainer = styled.div`
  width: 100%;
  height: 100%;
  position: relative;
  background: transparent;
`;

interface BadgeProps {
  isFlipped: boolean;
  onFlip: () => void;
}

const Badge3D: React.FC<BadgeProps> = ({ isFlipped, onFlip }) => {
  const meshRef = useRef<THREE.Group>(null);
  const { mouse, viewport } = useThree();
  
  // Mouse interaction for hanging effect
  useFrame(() => {
    if (meshRef.current) {
      const x = (mouse.x * viewport.width) / 2;
      const y = (mouse.y * viewport.height) / 2;
      
      // Smooth rotation based on mouse position
      meshRef.current.rotation.x = THREE.MathUtils.lerp(
        meshRef.current.rotation.x,
        y * 0.1,
        0.1
      );
      meshRef.current.rotation.y = THREE.MathUtils.lerp(
        meshRef.current.rotation.y,
        isFlipped ? Math.PI + x * 0.1 : x * 0.1,
        0.1
      );
      
      // Subtle position movement
      meshRef.current.position.x = THREE.MathUtils.lerp(
        meshRef.current.position.x,
        x * 0.1,
        0.1
      );
      meshRef.current.position.y = THREE.MathUtils.lerp(
        meshRef.current.position.y,
        y * 0.1,
        0.1
      );
    }
  });

  return (
    <Float
      speed={2}
      rotationIntensity={0.1}
      floatIntensity={0.2}
    >
      <group ref={meshRef} onClick={onFlip}>
        {/* Hanging String */}
        <mesh position={[0, 2, 0]}>
          <cylinderGeometry args={[0.01, 0.01, 4]} />
          <meshStandardMaterial color="#666666" />
        </mesh>
        
        {/* Main Badge */}
        <RoundedBox
          args={[4, 5, 0.2]}
          radius={0.2}
          smoothness={4}
          position={[0, 0, 0]}
        >
          <meshStandardMaterial
            color={isFlipped ? "#1a1a2e" : "#2a1a3e"}
            metalness={0.3}
            roughness={0.4}
          />
        </RoundedBox>
        
        {/* Badge Border */}
        <RoundedBox
          args={[4.1, 5.1, 0.15]}
          radius={0.2}
          smoothness={4}
          position={[0, 0, -0.05]}
        >
          <meshStandardMaterial
            color="#00ffff"
            emissive="#00ffff"
            emissiveIntensity={0.2}
            transparent
            opacity={0.3}
          />
        </RoundedBox>

        {!isFlipped ? (
          // Front Side Content
          <group>
            {/* Profile Circle Background */}
            <mesh position={[0, 1.5, 0.11]}>
              <circleGeometry args={[0.8]} />
              <meshStandardMaterial
                color="#00ffff"
                emissive="#00ffff"
                emissiveIntensity={0.1}
              />
            </mesh>
            
            {/* Profile Emoji */}
            <Text
              position={[0, 1.5, 0.12]}
              fontSize={1.2}
              color="#ffffff"
              anchorX="center"
              anchorY="middle"
            >
              👨‍💻
            </Text>
            
            {/* Name */}
            <Text
              position={[0, 0.5, 0.11]}
              fontSize={0.3}
              color="#ffffff"
              anchorX="center"
              anchorY="middle"
              maxWidth={3.5}
              textAlign="center"
            >
              {personalInfo.name}
            </Text>
            
            {/* Title */}
            <Text
              position={[0, 0, 0.11]}
              fontSize={0.2}
              color="#00ffff"
              anchorX="center"
              anchorY="middle"
              maxWidth={3.5}
              textAlign="center"
            >
              {personalInfo.title}
            </Text>
            
            {/* Experience Badge */}
            <RoundedBox
              args={[2.5, 0.6, 0.1]}
              radius={0.1}
              position={[0, -0.7, 0.11]}
            >
              <meshStandardMaterial
                color="#ffff00"
                emissive="#ffff00"
                emissiveIntensity={0.1}
              />
            </RoundedBox>
            
            <Text
              position={[0, -0.7, 0.16]}
              fontSize={0.15}
              color="#000000"
              anchorX="center"
              anchorY="middle"
            >
              {personalInfo.yearsOfExperience}+ Years Experience
            </Text>
            
            {/* Location */}
            <Text
              position={[0, -1.3, 0.11]}
              fontSize={0.15}
              color="#ffffff"
              anchorX="center"
              anchorY="middle"
              maxWidth={3.5}
              textAlign="center"
            >
              📍 {personalInfo.location}
            </Text>
            
            {/* Flip Indicator */}
            <Text
              position={[1.5, -2, 0.11]}
              fontSize={0.1}
              color="#00ffff"
              anchorX="center"
              anchorY="middle"
            >
              Click to flip →
            </Text>
          </group>
        ) : (
          // Back Side Content
          <group>
            {/* Contact Title */}
            <Text
              position={[0, 1.8, 0.11]}
              fontSize={0.4}
              color="#ffffff"
              anchorX="center"
              anchorY="middle"
            >
              Contact Info
            </Text>
            
            {/* Email */}
            <RoundedBox
              args={[3.5, 0.5, 0.05]}
              radius={0.05}
              position={[0, 1, 0.11]}
            >
              <meshStandardMaterial
                color="#333333"
                transparent
                opacity={0.8}
              />
            </RoundedBox>
            <Text
              position={[0, 1.1, 0.12]}
              fontSize={0.12}
              color="#00ffff"
              anchorX="center"
              anchorY="middle"
            >
              EMAIL
            </Text>
            <Text
              position={[0, 0.9, 0.12]}
              fontSize={0.1}
              color="#ffffff"
              anchorX="center"
              anchorY="middle"
              maxWidth={3.2}
            >
              {contactInfo.email}
            </Text>
            
            {/* GitHub */}
            <RoundedBox
              args={[3.5, 0.5, 0.05]}
              radius={0.05}
              position={[0, 0.2, 0.11]}
            >
              <meshStandardMaterial
                color="#333333"
                transparent
                opacity={0.8}
              />
            </RoundedBox>
            <Text
              position={[0, 0.3, 0.12]}
              fontSize={0.12}
              color="#00ffff"
              anchorX="center"
              anchorY="middle"
            >
              GITHUB
            </Text>
            <Text
              position={[0, 0.1, 0.12]}
              fontSize={0.1}
              color="#ffffff"
              anchorX="center"
              anchorY="middle"
              maxWidth={3.2}
            >
              {contactInfo.github.replace('https://', '')}
            </Text>
            
            {/* LinkedIn */}
            <RoundedBox
              args={[3.5, 0.5, 0.05]}
              radius={0.05}
              position={[0, -0.6, 0.11]}
            >
              <meshStandardMaterial
                color="#333333"
                transparent
                opacity={0.8}
              />
            </RoundedBox>
            <Text
              position={[0, -0.5, 0.12]}
              fontSize={0.12}
              color="#00ffff"
              anchorX="center"
              anchorY="middle"
            >
              LINKEDIN
            </Text>
            <Text
              position={[0, -0.7, 0.12]}
              fontSize={0.1}
              color="#ffffff"
              anchorX="center"
              anchorY="middle"
              maxWidth={3.2}
            >
              {contactInfo.linkedin.replace('https://', '')}
            </Text>
            
            {/* Flip Indicator */}
            <Text
              position={[-1.5, -2, 0.11]}
              fontSize={0.1}
              color="#00ffff"
              anchorX="center"
              anchorY="middle"
            >
              ← Click to flip
            </Text>
          </group>
        )}
      </group>
    </Float>
  );
};

export const Badge3DCanvas: React.FC = () => {
  const [isFlipped, setIsFlipped] = useState(false);

  const handleFlip = () => {
    setIsFlipped(!isFlipped);
  };

  return (
    <CanvasContainer>
      <Canvas
        camera={{ position: [0, 0, 8], fov: 50 }}
        style={{ background: 'transparent' }}
      >
        {/* Lighting Setup */}
        <ambientLight intensity={0.5} />
        <pointLight position={[10, 10, 10]} intensity={1} />
        <pointLight position={[-10, -10, -10]} intensity={0.5} color="#00ffff" />
        <spotLight
          position={[0, 10, 5]}
          angle={0.3}
          penumbra={1}
          intensity={1}
          castShadow
        />
        
        {/* 3D Badge */}
        <Badge3D isFlipped={isFlipped} onFlip={handleFlip} />
        
        {/* Background Particles */}
        {Array.from({ length: 30 }).map((_, i) => (
          <mesh
            key={i}
            position={[
              (Math.random() - 0.5) * 20,
              (Math.random() - 0.5) * 20,
              (Math.random() - 0.5) * 10
            ]}
          >
            <sphereGeometry args={[0.02]} />
            <meshStandardMaterial
              color="#00ffff"
              transparent
              opacity={0.3}
              emissive="#00ffff"
              emissiveIntensity={0.2}
            />
          </mesh>
        ))}
      </Canvas>
    </CanvasContainer>
  );
};
