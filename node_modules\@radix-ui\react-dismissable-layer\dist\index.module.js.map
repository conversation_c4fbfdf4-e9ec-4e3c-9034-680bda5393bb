{"mappings": ";;;;;;;;A;;;;;;;ACSA;;oGAEA,CAEA,MAAMW,4CAAsB,GAAG,kBAA/B,AAAA;AACA,MAAMC,oCAAc,GAAG,yBAAvB,AAAA;AACA,MAAMC,0CAAoB,GAAG,qCAA7B,AAAA;AACA,MAAMC,mCAAa,GAAG,+BAAtB,AAAA;AAEA,IAAIC,+CAAJ,AAAA;AAEA,MAAMC,6CAAuB,GAAA,aAAGZ,CAAAA,oBAAA,CAAoB;IAClDc,MAAM,EAAE,IAAIC,GAAJ,EAD0C;IAElDC,sCAAsC,EAAE,IAAID,GAAJ,EAFU;IAGlDE,QAAQ,EAAE,IAAIF,GAAJ,EAAVE;CAH8B,CAAhC,AAAoD;AA0CpD,MAAMrB,yCAAgB,GAAA,aAAGI,CAAAA,iBAAA,CACvB,CAACmB,KAAD,EAAQC,YAAR,GAAyB;IAAA,IAAA,mBAAA,AAAA;IACvB,MAAM,+BACJC,2BAA2B,GAAG,KAD1B,G,iBAEJC,eAFI,CAAA,E,sBAGJC,oBAHI,CAAA,E,gBAIJC,cAJI,CAAA,E,mBAKJC,iBALI,CAAA,E,WAMJC,SANI,CAAA,EAOJ,GAAGC,UAAH,EAPI,GAQFR,KARJ,AAAM;IASN,MAAMS,OAAO,GAAG5B,iBAAA,CAAiBY,6CAAjB,CAAhB,AAAA;IACA,MAAM,CAACkB,KAAD,EAAOC,OAAP,CAAA,GAAkB/B,eAAA,CAA+C,IAA/C,CAAxB,AAAA;IACA,MAAMiC,aAAa,GAAA,AAAA,CAAA,mBAAA,GAAGH,KAAH,KAAA,IAAA,IAAGA,KAAH,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAGA,KAAI,CAAEG,aAAT,CAAA,KAAA,IAAA,IAAA,mBAAA,KAAA,KAAA,CAAA,GAAA,mBAAA,GAA0BC,UAA1B,KAAA,IAAA,IAA0BA,UAA1B,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAA0BA,UAAU,CAAEC,QAAzD,AAAA;IACA,MAAM,GAAGC,KAAH,CAAA,GAAYpC,eAAA,CAAe,EAAf,CAAlB,AAAA;IACA,MAAMqC,YAAY,GAAGjC,sBAAe,CAACgB,YAAD,EAAgBU,CAAAA,IAAD,GAAUC,OAAO,CAACD,IAAD,CAAhC;IAAA,CAApC,AAAA;IACA,MAAMhB,MAAM,GAAGwB,KAAK,CAACC,IAAN,CAAWX,OAAO,CAACd,MAAnB,CAAf,AAAA;IACA,MAAM,CAAC0B,4CAAD,CAAA,GAAiD;WAAIZ,OAAO,CAACZ,sCAAZ;KAAA,CAAoDyB,KAApD,CAA0D,EAA1D,CAAvD,AAhBuB,EAgB+F,kBAAtH;IACA,MAAMC,iDAAiD,GAAG5B,MAAM,CAAC6B,OAAP,CAAeH,4CAAf,CAA1D,AAjBuB,EAiBiG,kBAAxH;IACA,MAAMI,KAAK,GAAGd,KAAI,GAAGhB,MAAM,CAAC6B,OAAP,CAAeb,KAAf,CAAH,GAA0B,EAA5C,AAAA;IACA,MAAMe,2BAA2B,GAAGjB,OAAO,CAACZ,sCAAR,CAA+C8B,IAA/C,GAAsD,CAA1F,AAAA;IACA,MAAMC,sBAAsB,GAAGH,KAAK,IAAIF,iDAAxC,AAAA;IAEA,MAAMM,kBAAkB,GAAGC,2CAAqB,CAAEC,CAAAA,KAAD,GAAW;QAC1D,MAAMC,MAAM,GAAGD,KAAK,CAACC,MAArB,AAAA;QACA,MAAMC,qBAAqB,GAAG;eAAIxB,OAAO,CAACX,QAAZ;SAAA,CAAsBoC,IAAtB,CAA4BC,CAAAA,MAAD,GAAYA,MAAM,CAACC,QAAP,CAAgBJ,MAAhB,CAAvC;QAAA,CAA9B,AAAA;QACA,IAAI,CAACJ,sBAAD,IAA2BK,qBAA/B,EAAsD,OAAtD;QACA7B,oBAAoB,KAAA,IAApB,IAAAA,oBAAoB,KAAA,KAAA,CAApB,IAAAA,oBAAoB,CAAG2B,KAAH,CAApB,CAAA3B;QACAE,iBAAiB,KAAA,IAAjB,IAAAA,iBAAiB,KAAA,KAAA,CAAjB,IAAAA,iBAAiB,CAAGyB,KAAH,CAAjB,CAAAzB;QACA,IAAI,CAACyB,KAAK,CAACM,gBAAX,EAA6B9B,SAAS,KAAA,IAAT,IAAAA,SAAS,KAAA,KAAA,CAAT,IAAAA,SAAS,EAAtC,CAAA;KAN8C,EAO7CO,aAP6C,CAAhD,AAOC;IAED,MAAMwB,YAAY,GAAGC,qCAAe,CAAER,CAAAA,KAAD,GAAW;QAC9C,MAAMC,MAAM,GAAGD,KAAK,CAACC,MAArB,AAAA;QACA,MAAMQ,eAAe,GAAG;eAAI/B,OAAO,CAACX,QAAZ;SAAA,CAAsBoC,IAAtB,CAA4BC,CAAAA,MAAD,GAAYA,MAAM,CAACC,QAAP,CAAgBJ,MAAhB,CAAvC;QAAA,CAAxB,AAAA;QACA,IAAIQ,eAAJ,EAAqB,OAArB;QACAnC,cAAc,KAAA,IAAd,IAAAA,cAAc,KAAA,KAAA,CAAd,IAAAA,cAAc,CAAG0B,KAAH,CAAd,CAAA1B;QACAC,iBAAiB,KAAA,IAAjB,IAAAA,iBAAiB,KAAA,KAAA,CAAjB,IAAAA,iBAAiB,CAAGyB,KAAH,CAAjB,CAAAzB;QACA,IAAI,CAACyB,KAAK,CAACM,gBAAX,EAA6B9B,SAAS,KAAA,IAAT,IAAAA,SAAS,KAAA,KAAA,CAAT,IAAAA,SAAS,EAAtC,CAAA;KANkC,EAOjCO,aAPiC,CAApC,AAOC;IAED3B,uBAAgB,CAAE4C,CAAAA,KAAD,GAAW;QAC1B,MAAMU,cAAc,GAAGhB,KAAK,KAAKhB,OAAO,CAACd,MAAR,CAAegC,IAAf,GAAsB,CAAvD,AAAA;QACA,IAAI,CAACc,cAAL,EAAqB,OAArB;QACAtC,eAAe,KAAA,IAAf,IAAAA,eAAe,KAAA,KAAA,CAAf,IAAAA,eAAe,CAAG4B,KAAH,CAAf,CAAA5B;QACA,IAAI,CAAC4B,KAAK,CAACM,gBAAP,IAA2B9B,SAA/B,EAA0C;YACxCwB,KAAK,CAACW,cAAN,EAAAX,CAAAA;YACAxB,SAAS,EAATA,CAAAA;SACD;KAPa,EAQbO,aARa,CAAhB,CAQC;IAEDjC,gBAAA,CAAgB,IAAM;QACpB,IAAI,CAAC8B,KAAL,EAAW,OAAX;QACA,IAAIT,2BAAJ,EAAiC;YAC/B,IAAIO,OAAO,CAACZ,sCAAR,CAA+C8B,IAA/C,KAAwD,CAA5D,EAA+D;gBAC7DnC,+CAAyB,GAAGsB,aAAa,CAAC8B,IAAd,CAAmBC,KAAnB,CAAyBC,aAArD,CAAAtD;gBACAsB,aAAa,CAAC8B,IAAd,CAAmBC,KAAnB,CAAyBC,aAAzB,GAAyC,MAAzC,CAAAhC;aACD;YACDL,OAAO,CAACZ,sCAAR,CAA+CkD,GAA/C,CAAmDpC,KAAnD,CAAAF,CAAAA;SACD;QACDA,OAAO,CAACd,MAAR,CAAeoD,GAAf,CAAmBpC,KAAnB,CAAAF,CAAAA;QACAuC,oCAAc,EAAdA,CAAAA;QACA,OAAO,IAAM;YACX,IACE9C,2BAA2B,IAC3BO,OAAO,CAACZ,sCAAR,CAA+C8B,IAA/C,KAAwD,CAF1D,EAIEb,aAAa,CAAC8B,IAAd,CAAmBC,KAAnB,CAAyBC,aAAzB,GAAyCtD,+CAAzC,CAAAsB;SALJ,CAOC;KAlBH,EAmBG;QAACH,KAAD;QAAOG,aAAP;QAAsBZ,2BAAtB;QAAmDO,OAAnD;KAnBH,CAmBC,CAAA;IAED;;;;;KAKJ,CACI5B,gBAAA,CAAgB,IAAM;QACpB,OAAO,IAAM;YACX,IAAI,CAAC8B,KAAL,EAAW,OAAX;YACAF,OAAO,CAACd,MAAR,CAAesD,MAAf,CAAsBtC,KAAtB,CAAAF,CAAAA;YACAA,OAAO,CAACZ,sCAAR,CAA+CoD,MAA/C,CAAsDtC,KAAtD,CAAAF,CAAAA;YACAuC,oCAAc,EAAdA,CAAAA;SAJF,CAKC;KANH,EAOG;QAACrC,KAAD;QAAOF,OAAP;KAPH,CAOC,CAAA;IAED5B,gBAAA,CAAgB,IAAM;QACpB,MAAMqE,YAAY,GAAG,IAAMjC,KAAK,CAAC,EAAD,CAAhC;QAAA;QACAD,QAAQ,CAACmC,gBAAT,CAA0B9D,oCAA1B,EAA0C6D,YAA1C,CAAAlC,CAAAA;QACA,OAAO,IAAMA,QAAQ,CAACoC,mBAAT,CAA6B/D,oCAA7B,EAA6C6D,YAA7C,CAAb;QAAA,CAAA;KAHF,EAIG,EAJH,CAIC,CAAA;IAED,OAAA,aACE,CAAA,oBAAA,CAAC,gBAAD,CAAW,GAAX,EAAA,oCAAA,CAAA,EAAA,EACM1C,UADN,EADF;QAGI,GAAG,EAAEU,YAFP;QAGE,KAAK,EAAE;YACL4B,aAAa,EAAEpB,2BAA2B,GACtCE,sBAAsB,GACpB,MADoB,GAEpB,MAHoC,GAItCyB,SALC;YAML,GAAGrD,KAAK,CAAC6C,KAAT;SATJ;QAWE,cAAc,EAAE/D,2BAAoB,CAACkB,KAAK,CAACsD,cAAP,EAAuBhB,YAAY,CAACgB,cAApC,CAXtC;QAYE,aAAa,EAAExE,2BAAoB,CAACkB,KAAK,CAACuD,aAAP,EAAsBjB,YAAY,CAACiB,aAAnC,CAZrC;QAaE,oBAAoB,EAAEzE,2BAAoB,CACxCkB,KAAK,CAACwD,oBADkC,EAExC3B,kBAAkB,CAAC2B,oBAFqB,CAA1C;KAbF,CAAA,CADF,CACE;CA9FmB,CAAzB,AAiHG;AAGH,aAAA,CAAA,MAAA,CAAA,MAAA,CAAA,yCAAA,EAAA;IAAA,WAAA,EAAA,4CAAA;CAAA,CAAA,CAAA;AAEA;;oGAEA,CAEA,MAAMC,iCAAW,GAAG,wBAApB,AAAA;AAKA,MAAM/E,yCAAsB,GAAA,aAAGG,CAAAA,iBAAA,CAG7B,CAACmB,KAAD,EAAQC,YAAR,GAAyB;IACzB,MAAMQ,OAAO,GAAG5B,iBAAA,CAAiBY,6CAAjB,CAAhB,AAAA;IACA,MAAMiE,GAAG,GAAG7E,aAAA,CAA4C,IAA5C,CAAZ,AAAA;IACA,MAAMqC,YAAY,GAAGjC,sBAAe,CAACgB,YAAD,EAAeyD,GAAf,CAApC,AAAA;IAEA7E,gBAAA,CAAgB,IAAM;QACpB,MAAM8B,IAAI,GAAG+C,GAAG,CAACE,OAAjB,AAAA;QACA,IAAIjD,IAAJ,EAAU;YACRF,OAAO,CAACX,QAAR,CAAiBiD,GAAjB,CAAqBpC,IAArB,CAAAF,CAAAA;YACA,OAAO,IAAM;gBACXA,OAAO,CAACX,QAAR,CAAiBmD,MAAjB,CAAwBtC,IAAxB,CAAAF,CAAAA;aADF,CAEC;SACF;KAPH,EAQG;QAACA,OAAO,CAACX,QAAT;KARH,CAQC,CAAA;IAED,OAAA,aAAO,CAAA,oBAAA,CAAC,gBAAD,CAAW,GAAX,EAAA,oCAAA,CAAA,EAAA,EAAmBE,KAAnB,EAAP;QAAiC,GAAG,EAAEkB,YAAL;KAA1B,CAAA,CAAP,CAAO;CAlBsB,CAA/B,AAmBC;AAED,aAAA,CAAA,MAAA,CAAA,MAAA,CAAA,yCAAA,EAAA;IAAA,WAAA,EAAA,iCAAA;CAAA,CAAA,CAAA;AAEA,oGAAA,CAKA;;;;GAIA,CACA,SAASY,2CAAT,CACE1B,oBADF,EAEEU,aAAuB,GAAGC,UAAH,KAAA,IAAA,IAAGA,UAAH,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAGA,UAAU,CAAEC,QAFxC,EAGE;IACA,MAAM6C,wBAAwB,GAAG3E,qBAAc,CAACkB,oBAAD,CAA/C,AAAA;IACA,MAAM0D,2BAA2B,GAAGjF,aAAA,CAAa,KAAb,CAApC,AAAA;IACA,MAAMkF,cAAc,GAAGlF,aAAA,CAAa,IAAM,EAAnB,CAAvB,AAAA;IAEAA,gBAAA,CAAgB,IAAM;QACpB,MAAMmF,iBAAiB,GAAIjC,CAAAA,KAAD,GAAyB;YACjD,IAAIA,KAAK,CAACC,MAAN,IAAgB,CAAC8B,2BAA2B,CAACF,OAAjD,EAA0D;gBACxD,MAAMK,WAAW,GAAG;oBAAEC,aAAa,EAAEnC,KAAfmC;iBAAtB,AAAoB;gBAEpB,SAASC,wCAAT,GAAoD;oBAClDC,kDAA4B,CAC1B9E,0CAD0B,EAE1BuE,wBAF0B,EAG1BI,WAH0B,EAI1B;wBAAEI,QAAQ,EAAE,IAAVA;qBAJwB,CAA5B,CAIE;iBAEH;gBAED;;;;;;;;;;;WAWR,CACQ,IAAItC,KAAK,CAACuC,WAAN,KAAsB,OAA1B,EAAmC;oBACjCxD,aAAa,CAACsC,mBAAd,CAAkC,OAAlC,EAA2CW,cAAc,CAACH,OAA1D,CAAA9C,CAAAA;oBACAiD,cAAc,CAACH,OAAf,GAAyBO,wCAAzB,CAAAJ;oBACAjD,aAAa,CAACqC,gBAAd,CAA+B,OAA/B,EAAwCY,cAAc,CAACH,OAAvD,EAAgE;wBAAEW,IAAI,EAAE,IAANA;qBAAlE,CAAgE,CAAA;iBAHlE,MAKEJ,wCAAwC,EAAxCA,CAAAA;aAEH;YACDL,2BAA2B,CAACF,OAA5B,GAAsC,KAAtC,CAAAE;SAjCF,AAkCC;QACD;;;;;;;;;;;;OAYJ,CACI,MAAMU,OAAO,GAAGC,MAAM,CAACC,UAAP,CAAkB,IAAM;YACtC5D,aAAa,CAACqC,gBAAd,CAA+B,aAA/B,EAA8Ca,iBAA9C,CAAAlD,CAAAA;SADc,EAEb,CAFa,CAAhB,AAEC;QACD,OAAO,IAAM;YACX2D,MAAM,CAACE,YAAP,CAAoBH,OAApB,CAAAC,CAAAA;YACA3D,aAAa,CAACsC,mBAAd,CAAkC,aAAlC,EAAiDY,iBAAjD,CAAAlD,CAAAA;YACAA,aAAa,CAACsC,mBAAd,CAAkC,OAAlC,EAA2CW,cAAc,CAACH,OAA1D,CAAA9C,CAAAA;SAHF,CAIC;KAxDH,EAyDG;QAACA,aAAD;QAAgB+C,wBAAhB;KAzDH,CAyDC,CAAA;IAED,OAAO;QACL,4DAAA;QACAL,oBAAoB,EAAE,IAAOM,2BAA2B,CAACF,OAA5B,GAAsC,IAAnEJ;KAFF,CAAO;CAIR;AAED;;;GAGA,CACA,SAASjB,qCAAT,CACElC,cADF,EAEES,aAAuB,GAAGC,UAAH,KAAA,IAAA,IAAGA,UAAH,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAGA,UAAU,CAAEC,QAFxC,EAGE;IACA,MAAM4D,kBAAkB,GAAG1F,qBAAc,CAACmB,cAAD,CAAzC,AAAA;IACA,MAAMwE,yBAAyB,GAAGhG,aAAA,CAAa,KAAb,CAAlC,AAAA;IAEAA,gBAAA,CAAgB,IAAM;QACpB,MAAMiG,WAAW,GAAI/C,CAAAA,KAAD,GAAuB;YACzC,IAAIA,KAAK,CAACC,MAAN,IAAgB,CAAC6C,yBAAyB,CAACjB,OAA/C,EAAwD;gBACtD,MAAMK,WAAW,GAAG;oBAAEC,aAAa,EAAEnC,KAAfmC;iBAAtB,AAAoB;gBACpBE,kDAA4B,CAAC7E,mCAAD,EAAgBqF,kBAAhB,EAAoCX,WAApC,EAAiD;oBAC3EI,QAAQ,EAAE,KAAVA;iBAD0B,CAA5B,CAA6E;aAG9E;SANH,AAOC;QACDvD,aAAa,CAACqC,gBAAd,CAA+B,SAA/B,EAA0C2B,WAA1C,CAAAhE,CAAAA;QACA,OAAO,IAAMA,aAAa,CAACsC,mBAAd,CAAkC,SAAlC,EAA6C0B,WAA7C,CAAb;QAAA,CAAA;KAVF,EAWG;QAAChE,aAAD;QAAgB8D,kBAAhB;KAXH,CAWC,CAAA;IAED,OAAO;QACLtB,cAAc,EAAE,IAAOuB,yBAAyB,CAACjB,OAA1B,GAAoC,IADtD;QAAA;QAELL,aAAa,EAAE,IAAOsB,yBAAyB,CAACjB,OAA1B,GAAoC,KAA1DL;KAFF,CAAO;CAIR;AAED,SAASP,oCAAT,GAA0B;IACxB,MAAMjB,KAAK,GAAG,IAAIgD,WAAJ,CAAgB1F,oCAAhB,CAAd,AAAA;IACA2B,QAAQ,CAACgE,aAAT,CAAuBjD,KAAvB,CAAAf,CAAAA;CACD;AAED,SAASoD,kDAAT,CACEa,IADF,EAEEC,OAFF,EAGEC,MAHF,EAIE,E,UAAEd,QAAAA,CAAAA,EAJJ,EAKE;IACA,MAAMrC,MAAM,GAAGmD,MAAM,CAACjB,aAAP,CAAqBlC,MAApC,AAAA;IACA,MAAMD,KAAK,GAAG,IAAIgD,WAAJ,CAAgBE,IAAhB,EAAsB;QAAEG,OAAO,EAAE,KAAX;QAAkBC,UAAU,EAAE,IAA9B;Q,QAAoCF,MAAAA;KAA1D,CAAd,AAAoC;IACpC,IAAID,OAAJ,EAAalD,MAAM,CAACmB,gBAAP,CAAwB8B,IAAxB,EAA8BC,OAA9B,EAAwD;QAAEX,IAAI,EAAE,IAANA;KAA1D,CAAwD,CAAA;IAErE,IAAIF,QAAJ,EACErF,kCAA2B,CAACgD,MAAD,EAASD,KAAT,CAA3B,CAAA/C;SAEAgD,MAAM,CAACgD,aAAP,CAAqBjD,KAArB,CAAAC,CAAAA;CAEH;AAED,MAAMrD,yCAAI,GAAGF,yCAAb,AAAA;AACA,MAAMG,yCAAM,GAAGF,yCAAf,AAAA;;AD5VA", "sources": ["packages/react/dismissable-layer/src/index.ts", "packages/react/dismissable-layer/src/DismissableLayer.tsx"], "sourcesContent": ["export {\n  DismissableLayer,\n  DismissableLayerBranch,\n  //\n  Root,\n  Branch,\n} from './DismissableLayer';\nexport type { DismissableLayerProps } from './DismissableLayer';\n", "import * as React from 'react';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { Primitive, dispatchDiscreteCustomEvent } from '@radix-ui/react-primitive';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { useCallbackRef } from '@radix-ui/react-use-callback-ref';\nimport { useEscapeKeydown } from '@radix-ui/react-use-escape-keydown';\n\nimport type * as Radix from '@radix-ui/react-primitive';\n\n/* -------------------------------------------------------------------------------------------------\n * DismissableLayer\n * -----------------------------------------------------------------------------------------------*/\n\nconst DISMISSABLE_LAYER_NAME = 'DismissableLayer';\nconst CONTEXT_UPDATE = 'dismissableLayer.update';\nconst POINTER_DOWN_OUTSIDE = 'dismissableLayer.pointerDownOutside';\nconst FOCUS_OUTSIDE = 'dismissableLayer.focusOutside';\n\nlet originalBodyPointerEvents: string;\n\nconst DismissableLayerContext = React.createContext({\n  layers: new Set<DismissableLayerElement>(),\n  layersWithOutsidePointerEventsDisabled: new Set<DismissableLayerElement>(),\n  branches: new Set<DismissableLayerBranchElement>(),\n});\n\ntype DismissableLayerElement = React.ElementRef<typeof Primitive.div>;\ntype PrimitiveDivProps = Radix.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface DismissableLayerProps extends PrimitiveDivProps {\n  /**\n   * When `true`, hover/focus/click interactions will be disabled on elements outside\n   * the `DismissableLayer`. Users will need to click twice on outside elements to\n   * interact with them: once to close the `DismissableLayer`, and again to trigger the element.\n   */\n  disableOutsidePointerEvents?: boolean;\n  /**\n   * Event handler called when the escape key is down.\n   * Can be prevented.\n   */\n  onEscapeKeyDown?: (event: KeyboardEvent) => void;\n  /**\n   * Event handler called when the a `pointerdown` event happens outside of the `DismissableLayer`.\n   * Can be prevented.\n   */\n  onPointerDownOutside?: (event: PointerDownOutsideEvent) => void;\n  /**\n   * Event handler called when the focus moves outside of the `DismissableLayer`.\n   * Can be prevented.\n   */\n  onFocusOutside?: (event: FocusOutsideEvent) => void;\n  /**\n   * Event handler called when an interaction happens outside the `DismissableLayer`.\n   * Specifically, when a `pointerdown` event happens outside or focus moves outside of it.\n   * Can be prevented.\n   */\n  onInteractOutside?: (event: PointerDownOutsideEvent | FocusOutsideEvent) => void;\n  /**\n   * Handler called when the `DismissableLayer` should be dismissed\n   */\n  onDismiss?: () => void;\n}\n\nconst DismissableLayer = React.forwardRef<DismissableLayerElement, DismissableLayerProps>(\n  (props, forwardedRef) => {\n    const {\n      disableOutsidePointerEvents = false,\n      onEscapeKeyDown,\n      onPointerDownOutside,\n      onFocusOutside,\n      onInteractOutside,\n      onDismiss,\n      ...layerProps\n    } = props;\n    const context = React.useContext(DismissableLayerContext);\n    const [node, setNode] = React.useState<DismissableLayerElement | null>(null);\n    const ownerDocument = node?.ownerDocument ?? globalThis?.document;\n    const [, force] = React.useState({});\n    const composedRefs = useComposedRefs(forwardedRef, (node) => setNode(node));\n    const layers = Array.from(context.layers);\n    const [highestLayerWithOutsidePointerEventsDisabled] = [...context.layersWithOutsidePointerEventsDisabled].slice(-1); // prettier-ignore\n    const highestLayerWithOutsidePointerEventsDisabledIndex = layers.indexOf(highestLayerWithOutsidePointerEventsDisabled); // prettier-ignore\n    const index = node ? layers.indexOf(node) : -1;\n    const isBodyPointerEventsDisabled = context.layersWithOutsidePointerEventsDisabled.size > 0;\n    const isPointerEventsEnabled = index >= highestLayerWithOutsidePointerEventsDisabledIndex;\n\n    const pointerDownOutside = usePointerDownOutside((event) => {\n      const target = event.target as HTMLElement;\n      const isPointerDownOnBranch = [...context.branches].some((branch) => branch.contains(target));\n      if (!isPointerEventsEnabled || isPointerDownOnBranch) return;\n      onPointerDownOutside?.(event);\n      onInteractOutside?.(event);\n      if (!event.defaultPrevented) onDismiss?.();\n    }, ownerDocument);\n\n    const focusOutside = useFocusOutside((event) => {\n      const target = event.target as HTMLElement;\n      const isFocusInBranch = [...context.branches].some((branch) => branch.contains(target));\n      if (isFocusInBranch) return;\n      onFocusOutside?.(event);\n      onInteractOutside?.(event);\n      if (!event.defaultPrevented) onDismiss?.();\n    }, ownerDocument);\n\n    useEscapeKeydown((event) => {\n      const isHighestLayer = index === context.layers.size - 1;\n      if (!isHighestLayer) return;\n      onEscapeKeyDown?.(event);\n      if (!event.defaultPrevented && onDismiss) {\n        event.preventDefault();\n        onDismiss();\n      }\n    }, ownerDocument);\n\n    React.useEffect(() => {\n      if (!node) return;\n      if (disableOutsidePointerEvents) {\n        if (context.layersWithOutsidePointerEventsDisabled.size === 0) {\n          originalBodyPointerEvents = ownerDocument.body.style.pointerEvents;\n          ownerDocument.body.style.pointerEvents = 'none';\n        }\n        context.layersWithOutsidePointerEventsDisabled.add(node);\n      }\n      context.layers.add(node);\n      dispatchUpdate();\n      return () => {\n        if (\n          disableOutsidePointerEvents &&\n          context.layersWithOutsidePointerEventsDisabled.size === 1\n        ) {\n          ownerDocument.body.style.pointerEvents = originalBodyPointerEvents;\n        }\n      };\n    }, [node, ownerDocument, disableOutsidePointerEvents, context]);\n\n    /**\n     * We purposefully prevent combining this effect with the `disableOutsidePointerEvents` effect\n     * because a change to `disableOutsidePointerEvents` would remove this layer from the stack\n     * and add it to the end again so the layering order wouldn't be _creation order_.\n     * We only want them to be removed from context stacks when unmounted.\n     */\n    React.useEffect(() => {\n      return () => {\n        if (!node) return;\n        context.layers.delete(node);\n        context.layersWithOutsidePointerEventsDisabled.delete(node);\n        dispatchUpdate();\n      };\n    }, [node, context]);\n\n    React.useEffect(() => {\n      const handleUpdate = () => force({});\n      document.addEventListener(CONTEXT_UPDATE, handleUpdate);\n      return () => document.removeEventListener(CONTEXT_UPDATE, handleUpdate);\n    }, []);\n\n    return (\n      <Primitive.div\n        {...layerProps}\n        ref={composedRefs}\n        style={{\n          pointerEvents: isBodyPointerEventsDisabled\n            ? isPointerEventsEnabled\n              ? 'auto'\n              : 'none'\n            : undefined,\n          ...props.style,\n        }}\n        onFocusCapture={composeEventHandlers(props.onFocusCapture, focusOutside.onFocusCapture)}\n        onBlurCapture={composeEventHandlers(props.onBlurCapture, focusOutside.onBlurCapture)}\n        onPointerDownCapture={composeEventHandlers(\n          props.onPointerDownCapture,\n          pointerDownOutside.onPointerDownCapture\n        )}\n      />\n    );\n  }\n);\n\nDismissableLayer.displayName = DISMISSABLE_LAYER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DismissableLayerBranch\n * -----------------------------------------------------------------------------------------------*/\n\nconst BRANCH_NAME = 'DismissableLayerBranch';\n\ntype DismissableLayerBranchElement = React.ElementRef<typeof Primitive.div>;\ninterface DismissableLayerBranchProps extends PrimitiveDivProps {}\n\nconst DismissableLayerBranch = React.forwardRef<\n  DismissableLayerBranchElement,\n  DismissableLayerBranchProps\n>((props, forwardedRef) => {\n  const context = React.useContext(DismissableLayerContext);\n  const ref = React.useRef<DismissableLayerBranchElement>(null);\n  const composedRefs = useComposedRefs(forwardedRef, ref);\n\n  React.useEffect(() => {\n    const node = ref.current;\n    if (node) {\n      context.branches.add(node);\n      return () => {\n        context.branches.delete(node);\n      };\n    }\n  }, [context.branches]);\n\n  return <Primitive.div {...props} ref={composedRefs} />;\n});\n\nDismissableLayerBranch.displayName = BRANCH_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\ntype PointerDownOutsideEvent = CustomEvent<{ originalEvent: PointerEvent }>;\ntype FocusOutsideEvent = CustomEvent<{ originalEvent: FocusEvent }>;\n\n/**\n * Listens for `pointerdown` outside a react subtree. We use `pointerdown` rather than `pointerup`\n * to mimic layer dismissing behaviour present in OS.\n * Returns props to pass to the node we want to check for outside events.\n */\nfunction usePointerDownOutside(\n  onPointerDownOutside?: (event: PointerDownOutsideEvent) => void,\n  ownerDocument: Document = globalThis?.document\n) {\n  const handlePointerDownOutside = useCallbackRef(onPointerDownOutside) as EventListener;\n  const isPointerInsideReactTreeRef = React.useRef(false);\n  const handleClickRef = React.useRef(() => {});\n\n  React.useEffect(() => {\n    const handlePointerDown = (event: PointerEvent) => {\n      if (event.target && !isPointerInsideReactTreeRef.current) {\n        const eventDetail = { originalEvent: event };\n\n        function handleAndDispatchPointerDownOutsideEvent() {\n          handleAndDispatchCustomEvent(\n            POINTER_DOWN_OUTSIDE,\n            handlePointerDownOutside,\n            eventDetail,\n            { discrete: true }\n          );\n        }\n\n        /**\n         * On touch devices, we need to wait for a click event because browsers implement\n         * a ~350ms delay between the time the user stops touching the display and when the\n         * browser executres events. We need to ensure we don't reactivate pointer-events within\n         * this timeframe otherwise the browser may execute events that should have been prevented.\n         *\n         * Additionally, this also lets us deal automatically with cancellations when a click event\n         * isn't raised because the page was considered scrolled/drag-scrolled, long-pressed, etc.\n         *\n         * This is why we also continuously remove the previous listener, because we cannot be\n         * certain that it was raised, and therefore cleaned-up.\n         */\n        if (event.pointerType === 'touch') {\n          ownerDocument.removeEventListener('click', handleClickRef.current);\n          handleClickRef.current = handleAndDispatchPointerDownOutsideEvent;\n          ownerDocument.addEventListener('click', handleClickRef.current, { once: true });\n        } else {\n          handleAndDispatchPointerDownOutsideEvent();\n        }\n      }\n      isPointerInsideReactTreeRef.current = false;\n    };\n    /**\n     * if this hook executes in a component that mounts via a `pointerdown` event, the event\n     * would bubble up to the document and trigger a `pointerDownOutside` event. We avoid\n     * this by delaying the event listener registration on the document.\n     * This is not React specific, but rather how the DOM works, ie:\n     * ```\n     * button.addEventListener('pointerdown', () => {\n     *   console.log('I will log');\n     *   document.addEventListener('pointerdown', () => {\n     *     console.log('I will also log');\n     *   })\n     * });\n     */\n    const timerId = window.setTimeout(() => {\n      ownerDocument.addEventListener('pointerdown', handlePointerDown);\n    }, 0);\n    return () => {\n      window.clearTimeout(timerId);\n      ownerDocument.removeEventListener('pointerdown', handlePointerDown);\n      ownerDocument.removeEventListener('click', handleClickRef.current);\n    };\n  }, [ownerDocument, handlePointerDownOutside]);\n\n  return {\n    // ensures we check React component tree (not just DOM tree)\n    onPointerDownCapture: () => (isPointerInsideReactTreeRef.current = true),\n  };\n}\n\n/**\n * Listens for when focus happens outside a react subtree.\n * Returns props to pass to the root (node) of the subtree we want to check.\n */\nfunction useFocusOutside(\n  onFocusOutside?: (event: FocusOutsideEvent) => void,\n  ownerDocument: Document = globalThis?.document\n) {\n  const handleFocusOutside = useCallbackRef(onFocusOutside) as EventListener;\n  const isFocusInsideReactTreeRef = React.useRef(false);\n\n  React.useEffect(() => {\n    const handleFocus = (event: FocusEvent) => {\n      if (event.target && !isFocusInsideReactTreeRef.current) {\n        const eventDetail = { originalEvent: event };\n        handleAndDispatchCustomEvent(FOCUS_OUTSIDE, handleFocusOutside, eventDetail, {\n          discrete: false,\n        });\n      }\n    };\n    ownerDocument.addEventListener('focusin', handleFocus);\n    return () => ownerDocument.removeEventListener('focusin', handleFocus);\n  }, [ownerDocument, handleFocusOutside]);\n\n  return {\n    onFocusCapture: () => (isFocusInsideReactTreeRef.current = true),\n    onBlurCapture: () => (isFocusInsideReactTreeRef.current = false),\n  };\n}\n\nfunction dispatchUpdate() {\n  const event = new CustomEvent(CONTEXT_UPDATE);\n  document.dispatchEvent(event);\n}\n\nfunction handleAndDispatchCustomEvent<E extends CustomEvent, OriginalEvent extends Event>(\n  name: string,\n  handler: ((event: E) => void) | undefined,\n  detail: { originalEvent: OriginalEvent } & (E extends CustomEvent<infer D> ? D : never),\n  { discrete }: { discrete: boolean }\n) {\n  const target = detail.originalEvent.target;\n  const event = new CustomEvent(name, { bubbles: false, cancelable: true, detail });\n  if (handler) target.addEventListener(name, handler as EventListener, { once: true });\n\n  if (discrete) {\n    dispatchDiscreteCustomEvent(target, event);\n  } else {\n    target.dispatchEvent(event);\n  }\n}\n\nconst Root = DismissableLayer;\nconst Branch = DismissableLayerBranch;\n\nexport {\n  DismissableLayer,\n  DismissableLayerBranch,\n  //\n  Root,\n  Branch,\n};\nexport type { DismissableLayerProps };\n"], "names": ["Dismissa<PERSON><PERSON><PERSON><PERSON>", "DismissableLayerBranch", "Root", "Branch", "React", "composeEventHandlers", "Primitive", "dispatchDiscreteCustomEvent", "useComposedRefs", "useCallbackRef", "useEscapeKeydown", "DISMISSABLE_LAYER_NAME", "CONTEXT_UPDATE", "POINTER_DOWN_OUTSIDE", "FOCUS_OUTSIDE", "originalBodyPointerEvents", "DismissableLayerContext", "createContext", "layers", "Set", "layersWithOutsidePointerEventsDisabled", "branches", "forwardRef", "props", "forwardedRef", "disableOutsidePointerEvents", "onEscapeKeyDown", "onPointerDownOutside", "onFocusOutside", "onInteractOutside", "on<PERSON><PERSON><PERSON>", "layerProps", "context", "useContext", "node", "setNode", "useState", "ownerDocument", "globalThis", "document", "force", "composedRefs", "Array", "from", "highestLayerWithOutsidePointerEventsDisabled", "slice", "highestLayerWithOutsidePointerEventsDisabledIndex", "indexOf", "index", "isBodyPointerEventsDisabled", "size", "isPointerEventsEnabled", "pointerDownOutside", "usePointerDownOutside", "event", "target", "isPointerDownOnBranch", "some", "branch", "contains", "defaultPrevented", "focusOutside", "useFocusOutside", "isFocusInBranch", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "preventDefault", "useEffect", "body", "style", "pointerEvents", "add", "dispatchUpdate", "delete", "handleUpdate", "addEventListener", "removeEventListener", "undefined", "onFocusCapture", "onBlurCapture", "onPointerDownCapture", "BRANCH_NAME", "ref", "useRef", "current", "handlePointerDownOutside", "isPointerInsideReactTreeRef", "handleClickRef", "handlePointerDown", "eventDetail", "originalEvent", "handleAndDispatchPointerDownOutsideEvent", "handleAndDispatchCustomEvent", "discrete", "pointerType", "once", "timerId", "window", "setTimeout", "clearTimeout", "handleFocusOutside", "isFocusInsideReactTreeRef", "handleFocus", "CustomEvent", "dispatchEvent", "name", "handler", "detail", "bubbles", "cancelable"], "version": 3, "file": "index.module.js.map"}