{"mappings": ";;;;;AAyBA,OAAA,MAAM,yDAA0D,CAAC;AACjE,OAAA,MAAM,kDAAmD,CAAC;AAE1D,YAAY,mBAAmB,CAAC,MAAM,CAAC,CAAC;AACxC,aAAa,oBAAoB,CAAC,MAAM,CAAC,CAAC;AAS1C,OAAA,wFAAgF,CAAC;AAQjF;IACE,QAAQ,CAAC,EAAE,MAAM,SAAS,CAAC;CAC5B;AACD,OAAA,MAAM,QAAQ,MAAM,EAAE,CAAC,WAAW,CAQjC,CAAC;AAWF,yBAAyB,MAAM,wBAAwB,CAAC,OAAO,UAAU,GAAG,CAAC,CAAC;AAC9E,kCAA4B,SAAQ,iBAAiB;IACnD,UAAU,CAAC,EAAE,MAAM,SAAS,CAAC,UAAU,CAAC,CAAC;CAC1C;AAED,OAAA,MAAM,sGAgBL,CAAC;AA0BF,gBAAgB,OAAO,GAAG,IAAI,CAAC;AAG/B,mCAA6B,SAAQ,iBAAiB;IACpD,IAAI,CAAC,EAAE,IAAI,CAAC;IACZ,UAAU,CAAC,EAAE,MAAM,CAAC;IACpB,KAAK,CAAC,EAAE,KAAK,CAAC;IACd,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB,YAAY,CAAC,EAAE,MAAM,CAAC;IACtB,iBAAiB,CAAC,EAAE,QAAQ,GAAG,QAAQ,EAAE,CAAC;IAC1C,gBAAgB,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC;IAC1D,MAAM,CAAC,EAAE,SAAS,GAAG,QAAQ,CAAC;IAC9B,gBAAgB,CAAC,EAAE,OAAO,CAAC;IAC3B,eAAe,CAAC,EAAE,OAAO,CAAC;IAC1B,QAAQ,CAAC,EAAE,MAAM,IAAI,CAAC;CACvB;AAED,OAAA,MAAM,wGAwLL,CAAC;AAkBF,kBAAkB,MAAM,wBAAwB,CAAC,OAAO,eAAe,IAAI,CAAC,CAAC;AAC7E,iCAA2B,SAAQ,UAAU;CAAG;AAEhD,OAAA,MAAM,mGA6CJ,CAAC;AAmEH,OAAA,MAAM,2BAAa,CAAC;AACpB,OAAA,MAAM,gGAAqB,CAAC;AAC5B,OAAA,MAAM,kGAAuB,CAAC;AAC9B,OAAA,MAAM,6FAAmB,CAAC", "sources": ["packages/react/popper/src/packages/react/popper/src/Popper.tsx", "packages/react/popper/src/packages/react/popper/src/index.ts", "packages/react/popper/src/index.ts"], "sourcesContent": [null, null, "export {\n  createPopperScope,\n  //\n  <PERSON><PERSON>,\n  <PERSON>perAnchor,\n  PopperContent,\n  PopperArrow,\n  //\n  Root,\n  Anchor,\n  Content,\n  Arrow,\n  //\n  SIDE_OPTIONS,\n  ALIGN_OPTIONS,\n} from './Popper';\nexport type {\n  PopperProps,\n  PopperAnchorProps,\n  PopperContentProps,\n  PopperArrowProps,\n} from './Popper';\n"], "names": [], "version": 3, "file": "index.d.ts.map"}