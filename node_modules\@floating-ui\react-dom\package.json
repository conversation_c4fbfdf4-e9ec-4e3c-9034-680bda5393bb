{"name": "@floating-ui/react-dom", "version": "0.7.2", "@rollingversions": {"baseVersion": [0, 0, 0]}, "description": "Floating UI for React DOM", "publishConfig": {"access": "public"}, "main": "./dist/floating-ui.react-dom.js", "module": "./dist/floating-ui.react-dom.esm.js", "unpkg": "./dist/floating-ui.react-dom.min.js", "types": "./index.d.ts", "exports": {".": {"types": "./index.d.ts", "module": "./dist/floating-ui.react-dom.esm.js", "import": "./dist/floating-ui.react-dom.mjs", "default": "./dist/floating-ui.react-dom.js"}, "./package.json": "./package.json"}, "sideEffects": false, "files": ["dist/", "index.d.ts", "src/**/*.d.ts"], "browserslist": "> 0.5%, not dead, not IE 11", "scripts": {"test": "jest test", "build": "NODE_ENV=build rollup -c"}, "author": "atomiks", "license": "MIT", "bugs": "https://github.com/floating-ui/floating-ui", "repository": {"type": "git", "url": "https://github.com/floating-ui/floating-ui.git", "directory": "packages/react-dom"}, "homepage": "https://floating-ui.com/docs/react-dom", "keywords": ["tooltip", "popover", "dropdown", "menu", "popup", "positioning", "react", "react-dom"], "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dependencies": {"@floating-ui/dom": "^0.5.3", "use-isomorphic-layout-effect": "^1.1.1"}, "devDependencies": {"@babel/preset-env": "^7.16.4", "@babel/preset-react": "^7.16.0", "@babel/preset-typescript": "^7.16.0", "@rollup/plugin-babel": "^5.3.0", "@rollup/plugin-commonjs": "^21.0.1", "@rollup/plugin-node-resolve": "^13.0.6", "@rollup/plugin-replace": "^3.0.0", "@testing-library/react": "^13.2.0", "@types/jest": "^27.0.3", "@types/react": "^18.0.1", "babel-plugin-annotate-pure-calls": "^0.4.0", "jest": "^27.3.1", "react": "^18.0.0", "react-dom": "^18.0.0", "rollup": "^2.60.1", "rollup-plugin-terser": "^7.0.2", "ts-jest": "^27.0.7"}}