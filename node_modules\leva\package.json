{"name": "leva", "version": "0.10.0", "main": "dist/leva.cjs.js", "module": "dist/leva.esm.js", "types": "dist/leva.cjs.d.ts", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/pmndrs/leva.git", "directory": "packages/leva"}, "bugs": "https://github.com/pmndrs/leva/issues", "preconstruct": {"entrypoints": ["index.ts", "plugin/index.ts"]}, "peerDependencies": {"react": "^18.0.0 || ^19.0.0", "react-dom": "^18.0.0 || ^19.0.0"}, "dependencies": {"@radix-ui/react-portal": "1.0.2", "@radix-ui/react-tooltip": "1.0.5", "@stitches/react": "^1.2.8", "@use-gesture/react": "^10.2.5", "colord": "^2.9.2", "dequal": "^2.0.2", "merge-value": "^1.0.0", "react-colorful": "^5.5.1", "react-dropzone": "^12.0.0", "v8n": "^1.3.3", "zustand": "^3.6.9"}, "devDependencies": {"@welldone-software/why-did-you-render": "^6.2.3"}}