# 🎨 Customization Guide

This guide will help you personalize your terminal portfolio website.

## 📝 Step 1: Update Personal Information

Edit `src/data/portfolio.ts` and update the following sections:

### Personal Info
```typescript
export const personalInfo = {
  name: "Your Full Name",                    // Replace with your name
  title: "Your Professional Title",          // e.g., "Full Stack Developer"
  bio: "Your professional bio...",           // Brief description about yourself
  location: "Your City, Country",           // Your location
  yearsOfExperience: 3                      // Your years of experience
};
```

### Contact Information
```typescript
export const contactInfo: ContactInfo = {
  email: "<EMAIL>",          // Your email
  phone: "+****************",              // Your phone (optional)
  location: "Your City, Country",           // Your location
  linkedin: "https://linkedin.com/in/yourprofile",  // Your LinkedIn
  github: "https://github.com/yourusername",        // Your GitHub
  website: "https://yourwebsite.com"       // Your website (optional)
};
```

## 🛠️ Step 2: Update Skills

Organize your technical skills by category:

```typescript
export const skills = {
  languages: ["JavaScript", "TypeScript", "Python", "Your Languages"],
  frontend: ["React", "Vue.js", "Your Frontend Tech"],
  backend: ["Node.js", "Express", "Your Backend Tech"],
  databases: ["PostgreSQL", "MongoDB", "Your Databases"],
  tools: ["Docker", "AWS", "Your Tools"],
  other: ["GraphQL", "Your Other Skills"]
};
```

## 📁 Step 3: Add Your Projects

Update the projects array with your actual projects:

```typescript
export const projects: Project[] = [
  {
    id: "1",
    name: "Your Project Name",
    description: "Brief description of your project",
    technologies: ["React", "Node.js", "PostgreSQL"],
    githubUrl: "https://github.com/yourusername/project",
    liveUrl: "https://yourproject.com",      // Optional
    featured: true                           // Show in featured section
  },
  // Add more projects...
];
```

## 💼 Step 4: Add Work Experience

```typescript
export const experience: Experience[] = [
  {
    id: "1",
    company: "Your Company Name",
    position: "Your Position",
    duration: "Start Date - End Date",
    description: "What you did at this company...",
    technologies: ["Technologies", "You", "Used"]
  },
  // Add more experiences...
];
```

## 🎓 Step 5: Add Education

```typescript
export const education: Education[] = [
  {
    id: "1",
    institution: "Your University/School",
    degree: "Your Degree",
    duration: "Start Year - End Year",
    description: "Additional details (optional)"
  },
  // Add more education entries...
];
```

## 📄 Step 6: Replace Resume

1. Replace `public/resume.pdf` with your actual resume file
2. Keep the filename as `resume.pdf` or update the command in `src/utils/commands.ts`

## 🎨 Step 7: Customize Themes (Optional)

Add your own terminal theme in `src/styles/themes.ts`:

```typescript
yourtheme: {
  name: 'Your Theme Name',
  background: '#your-background-color',
  foreground: '#your-text-color',
  cursor: '#your-cursor-color',
  selection: '#your-selection-color',
  // Add all required colors...
}
```

## ⚡ Step 8: Add Custom Commands (Optional)

Add new commands in `src/utils/commands.ts`:

```typescript
yourcommand: {
  name: 'yourcommand',
  description: 'Description of your command',
  usage: 'yourcommand [optional-args]',     // Optional
  aliases: ['alias1', 'alias2'],            // Optional
  execute: (args: string[]) => {
    // Your command logic here
    return createOutput('Your output', 'success');
  }
}
```

## 🖼️ Step 9: Update Profile Image (Optional)

The profile card currently uses an emoji (👨‍💻). To use a real image:

1. Add your image to `public/` folder
2. Update `ProfileImage` component in `src/components/Profile/ProfileCard.tsx`:

```typescript
const ProfileImage = styled.div`
  // ... existing styles
  background-image: url('/your-image.jpg');
  background-size: cover;
  background-position: center;
  
  &::before {
    content: '';  // Remove emoji
    // ... rest of styles
  }
`;
```

## 🚀 Step 10: Test Your Changes

1. Save all files
2. The development server will automatically reload
3. Test all commands in the terminal
4. Check that all links work correctly
5. Verify the profile card displays correctly

## 📱 Step 11: Mobile Optimization

The site is already responsive, but you can customize mobile behavior in:
- `src/styles/GlobalStyles.ts` - Media queries
- `src/components/Profile/ProfileCard.tsx` - Profile card responsive behavior

## 🎯 Quick Checklist

- [ ] Updated personal information
- [ ] Added your skills
- [ ] Added your projects
- [ ] Added work experience
- [ ] Added education
- [ ] Replaced resume PDF
- [ ] Tested all terminal commands
- [ ] Verified all links work
- [ ] Checked mobile responsiveness

## 🔧 Troubleshooting

### Command not working?
- Check `src/utils/commands.ts` for typos
- Ensure the command is added to the `commands` object
- Check browser console for errors

### Styling issues?
- Check `src/styles/themes.ts` for theme-related issues
- Verify styled-components syntax
- Check browser developer tools

### Build errors?
- Run `npm run build` to check for TypeScript errors
- Fix any type errors in the code
- Ensure all imports are correct

---

**Need help?** Check the main README.md or create an issue in the repository!
