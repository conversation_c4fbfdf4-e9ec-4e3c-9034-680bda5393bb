{"name": "terminal-portfolio", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0"}, "dependencies": {"framer-motion": "^10.18.0", "react": "^18.3.1", "react-dom": "^18.3.1", "styled-components": "^6.1.19"}, "devDependencies": {"@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "@types/styled-components": "^5.1.34", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.6.0", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "typescript": "^5.8.3", "vite": "^5.4.19"}, "main": "index.js", "keywords": [], "author": "", "license": "ISC", "description": ""}