import React from 'react'
import ReactDOM from 'react-dom/client'

const App = () => {
  return (
    <div style={{
      background: '#0a0a0a',
      color: '#ffffff',
      height: '100vh',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      fontFamily: 'monospace',
      fontSize: '18px'
    }}>
      <div style={{ textAlign: 'center' }}>
        <h1 style={{ color: '#00ff00', marginBottom: '20px' }}>🚀 Terminal Portfolio</h1>
        <p style={{ color: '#00ffff', marginBottom: '10px' }}>✅ React is working!</p>
        <p style={{ color: '#ffff00', marginBottom: '10px' }}>✅ Vite is serving the app!</p>
        <p style={{ color: '#ffffff', marginBottom: '20px' }}>Now loading the full portfolio...</p>
        <div style={{ color: '#00ff00' }}>
          <div>Loading terminal interface...</div>
          <div>Loading 3D components...</div>
        </div>
      </div>
    </div>
  );
};

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <App />
  </React.StrictMode>,
)
