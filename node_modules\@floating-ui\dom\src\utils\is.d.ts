declare global {
    interface Window {
        HTMLElement: any;
        Element: any;
        Node: any;
        ShadowRoot: any;
    }
}
export declare function isHTMLElement(value: any): value is HTMLElement;
export declare function isElement(value: any): value is Element;
export declare function isNode(value: any): value is Node;
export declare function isShadowRoot(node: Node): node is ShadowRoot;
export declare function isOverflowElement(element: HTMLElement): boolean;
export declare function isTableElement(element: Element): boolean;
export declare function isContainingBlock(element: Element): boolean;
export declare function isLayoutViewport(): boolean;
