import $kVwnw$babelruntimehelpersesmextends from "@babel/runtime/helpers/esm/extends";
import {forwardRef as $kVwnw$forwardRef, createElement as $kVwnw$createElement} from "react";
import {Primitive as $kVwnw$Primitive} from "@radix-ui/react-primitive";




/* -------------------------------------------------------------------------------------------------
 * VisuallyHidden
 * -----------------------------------------------------------------------------------------------*/ const $ea1ef594cf570d83$var$NAME = 'VisuallyHidden';
const $ea1ef594cf570d83$export$439d29a4e110a164 = /*#__PURE__*/ $kVwnw$forwardRef((props, forwardedRef)=>{
    return /*#__PURE__*/ $kVwnw$createElement($kVwnw$Primitive.span, $kVwnw$babelruntimehelpersesmextends({}, props, {
        ref: forwardedRef,
        style: {
            // See: https://github.com/twbs/bootstrap/blob/master/scss/mixins/_screen-reader.scss
            position: 'absolute',
            border: 0,
            width: 1,
            height: 1,
            padding: 0,
            margin: -1,
            overflow: 'hidden',
            clip: 'rect(0, 0, 0, 0)',
            whiteSpace: 'nowrap',
            wordWrap: 'normal',
            ...props.style
        }
    }));
});
/*#__PURE__*/ Object.assign($ea1ef594cf570d83$export$439d29a4e110a164, {
    displayName: $ea1ef594cf570d83$var$NAME
});
/* -----------------------------------------------------------------------------------------------*/ const $ea1ef594cf570d83$export$be92b6f5f03c0fe9 = $ea1ef594cf570d83$export$439d29a4e110a164;




export {$ea1ef594cf570d83$export$439d29a4e110a164 as VisuallyHidden, $ea1ef594cf570d83$export$be92b6f5f03c0fe9 as Root};
//# sourceMappingURL=index.module.js.map
