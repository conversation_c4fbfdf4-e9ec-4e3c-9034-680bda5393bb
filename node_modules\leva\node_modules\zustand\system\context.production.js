System.register(["react"],function(v){"use strict";var a,u,c,s,d;return{setters:[function(t){a=t.createContext,u=t.useRef,c=t.createElement,s=t.useContext,d=t.useMemo}],execute:function(){v("default",t);function t(){const o=a(void 0);return{Provider:({initialStore:e,createStore:r,children:n})=>{const i=u();return i.current||(e&&(console.warn("Provider initialStore is deprecated and will be removed in the next version."),r||(r=()=>e)),i.current=r()),c(o.Provider,{value:i.current},n)},useStore:(e,r=Object.is)=>{const n=s(o);if(!n)throw new Error("Seems like you have not used zustand provider as an ancestor.");return n(e,r)},useStoreApi:()=>{const e=s(o);if(!e)throw new Error("Seems like you have not used zustand provider as an ancestor.");return d(()=>({getState:e.getState,setState:e.setState,subscribe:e.subscribe,destroy:e.destroy}),[e])}}}}}});
