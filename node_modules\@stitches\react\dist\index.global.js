stitches=(()=>{var e,t="borderStyles",n="borderWidths",r="colors",i="radii",o="shadows",l="sizes",s="space",a={gap:s,gridGap:s,columnGap:s,gridColumnGap:s,rowGap:s,gridRowGap:s,inset:s,insetBlock:s,insetBlockEnd:s,insetBlockStart:s,insetInline:s,insetInlineEnd:s,insetInlineStart:s,margin:s,marginTop:s,marginRight:s,marginBottom:s,marginLeft:s,marginBlock:s,marginBlockEnd:s,marginBlockStart:s,marginInline:s,marginInlineEnd:s,marginInlineStart:s,padding:s,paddingTop:s,paddingRight:s,paddingBottom:s,paddingLeft:s,paddingBlock:s,paddingBlockEnd:s,paddingBlockStart:s,paddingInline:s,paddingInlineEnd:s,paddingInlineStart:s,top:s,right:s,bottom:s,left:s,scrollMargin:s,scrollMarginTop:s,scrollMarginRight:s,scrollMarginBottom:s,scrollMarginLeft:s,scrollMarginX:s,scrollMarginY:s,scrollMarginBlock:s,scrollMarginBlockEnd:s,scrollMarginBlockStart:s,scrollMarginInline:s,scrollMarginInlineEnd:s,scrollMarginInlineStart:s,scrollPadding:s,scrollPaddingTop:s,scrollPaddingRight:s,scrollPaddingBottom:s,scrollPaddingLeft:s,scrollPaddingX:s,scrollPaddingY:s,scrollPaddingBlock:s,scrollPaddingBlockEnd:s,scrollPaddingBlockStart:s,scrollPaddingInline:s,scrollPaddingInlineEnd:s,scrollPaddingInlineStart:s,fontSize:"fontSizes",background:r,backgroundColor:r,backgroundImage:r,borderImage:r,border:r,borderBlock:r,borderBlockEnd:r,borderBlockStart:r,borderBottom:r,borderBottomColor:r,borderColor:r,borderInline:r,borderInlineEnd:r,borderInlineStart:r,borderLeft:r,borderLeftColor:r,borderRight:r,borderRightColor:r,borderTop:r,borderTopColor:r,caretColor:r,color:r,columnRuleColor:r,fill:r,outline:r,outlineColor:r,stroke:r,textDecorationColor:r,fontFamily:"fonts",fontWeight:"fontWeights",lineHeight:"lineHeights",letterSpacing:"letterSpacings",blockSize:l,minBlockSize:l,maxBlockSize:l,inlineSize:l,minInlineSize:l,maxInlineSize:l,width:l,minWidth:l,maxWidth:l,height:l,minHeight:l,maxHeight:l,flexBasis:l,gridTemplateColumns:l,gridTemplateRows:l,borderWidth:n,borderTopWidth:n,borderRightWidth:n,borderBottomWidth:n,borderLeftWidth:n,borderStyle:t,borderTopStyle:t,borderRightStyle:t,borderBottomStyle:t,borderLeftStyle:t,borderRadius:i,borderTopLeftRadius:i,borderTopRightRadius:i,borderBottomRightRadius:i,borderBottomLeftRadius:i,boxShadow:o,textShadow:o,transition:"transitions",zIndex:"zIndices"},c=(e,t)=>"function"==typeof t?{"()":Function.prototype.toString.call(t)}:t,d=()=>{const e=Object.create(null);return(t,n,...r)=>{const i=(e=>JSON.stringify(e,c))(t);return i in e?e[i]:e[i]=n(t,...r)}},g=Symbol.for("sxs.internal"),p=(e,t)=>Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)),u=e=>{for(const t in e)return!0;return!1},{hasOwnProperty:h}=Object.prototype,f=e=>e.includes("-")?e:e.replace(/[A-Z]/g,(e=>"-"+e.toLowerCase())),m=/\s+(?![^()]*\))/,b=e=>t=>e(..."string"==typeof t?String(t).split(m):[t]),S={appearance:e=>({WebkitAppearance:e,appearance:e}),backfaceVisibility:e=>({WebkitBackfaceVisibility:e,backfaceVisibility:e}),backdropFilter:e=>({WebkitBackdropFilter:e,backdropFilter:e}),backgroundClip:e=>({WebkitBackgroundClip:e,backgroundClip:e}),boxDecorationBreak:e=>({WebkitBoxDecorationBreak:e,boxDecorationBreak:e}),clipPath:e=>({WebkitClipPath:e,clipPath:e}),content:e=>({content:e.includes('"')||e.includes("'")||/^([A-Za-z]+\([^]*|[^]*-quote|inherit|initial|none|normal|revert|unset)$/.test(e)?e:`"${e}"`}),hyphens:e=>({WebkitHyphens:e,hyphens:e}),maskImage:e=>({WebkitMaskImage:e,maskImage:e}),maskSize:e=>({WebkitMaskSize:e,maskSize:e}),tabSize:e=>({MozTabSize:e,tabSize:e}),textSizeAdjust:e=>({WebkitTextSizeAdjust:e,textSizeAdjust:e}),userSelect:e=>({WebkitUserSelect:e,userSelect:e}),marginBlock:b(((e,t)=>({marginBlockStart:e,marginBlockEnd:t||e}))),marginInline:b(((e,t)=>({marginInlineStart:e,marginInlineEnd:t||e}))),maxSize:b(((e,t)=>({maxBlockSize:e,maxInlineSize:t||e}))),minSize:b(((e,t)=>({minBlockSize:e,minInlineSize:t||e}))),paddingBlock:b(((e,t)=>({paddingBlockStart:e,paddingBlockEnd:t||e}))),paddingInline:b(((e,t)=>({paddingInlineStart:e,paddingInlineEnd:t||e})))},k=/([\d.]+)([^]*)/,y=(e,t)=>e.length?e.reduce(((e,n)=>(e.push(...t.map((e=>e.includes("&")?e.replace(/&/g,/[ +>|~]/.test(n)&&/&.*&/.test(e)?`:is(${n})`:n):n+" "+e))),e)),[]):t,B=(e,t)=>e in $&&"string"==typeof t?t.replace(/^((?:[^]*[^\w-])?)(fit-content|stretch)((?:[^\w-][^]*)?)$/,((t,n,r,i)=>n+("stretch"===r?`-moz-available${i};${f(e)}:${n}-webkit-fill-available`:`-moz-fit-content${i};${f(e)}:${n}fit-content`)+i)):String(t),$={blockSize:1,height:1,inlineSize:1,maxBlockSize:1,maxHeight:1,maxInlineSize:1,maxWidth:1,minBlockSize:1,minHeight:1,minInlineSize:1,minWidth:1,width:1},R=e=>e?e+"-":"",x=(e,t,n)=>e.replace(/([+-])?((?:\d+(?:\.\d*)?|\.\d+)(?:[Ee][+-]?\d+)?)?(\$|--)([$\w-]+)/g,((e,r,i,o,l)=>"$"==o==!!i?e:(r||"--"==o?"calc(":"")+"var(--"+("$"===o?R(t)+(l.includes("$")?"":R(n))+l.replace(/\$/g,"-"):l)+")"+(r||"--"==o?"*"+(r||"")+(i||"1")+")":""))),I=/\s*,\s*(?![^()]*\))/,z=Object.prototype.toString,j=(e,t,n,r,i)=>{let o,l,s;const a=(e,t,n)=>{let c,d;const g=e=>{for(c in e){const h=64===c.charCodeAt(0),m=h&&Array.isArray(e[c])?e[c]:[e[c]];for(d of m){const e=/[A-Z]/.test(u=c)?u:u.replace(/-[^]/g,(e=>e[1].toUpperCase())),m="object"==typeof d&&d&&d.toString===z&&(!r.utils[e]||!t.length);if(e in r.utils&&!m){const t=r.utils[e];if(t!==l){l=t,g(t(d)),l=null;continue}}else if(e in S){const t=S[e];if(t!==s){s=t,g(t(d)),s=null;continue}}if(h&&(p=c.slice(1)in r.media?"@media "+r.media[c.slice(1)]:c,c=p.replace(/\(\s*([\w-]+)\s*(=|<|<=|>|>=)\s*([\w-]+)\s*(?:(<|<=|>|>=)\s*([\w-]+)\s*)?\)/g,((e,t,n,r,i,o)=>{const l=k.test(t),s=.0625*(l?-1:1),[a,c]=l?[r,t]:[t,r];return"("+("="===n[0]?"":">"===n[0]===l?"max-":"min-")+a+":"+("="!==n[0]&&1===n.length?c.replace(k,((e,t,r)=>Number(t)+s*(">"===n?1:-1)+r)):c)+(i?") and ("+(">"===i[0]?"min-":"max-")+a+":"+(1===i.length?o.replace(k,((e,t,n)=>Number(t)+s*(">"===i?-1:1)+n)):o):"")+")"}))),m){const e=h?n.concat(c):[...n],r=h?[...t]:y(t,c.split(I));void 0!==o&&i(W(...o)),o=void 0,a(d,r,e)}else void 0===o&&(o=[[],t,n]),c=h||36!==c.charCodeAt(0)?c:`--${R(r.prefix)}${c.slice(1).replace(/\$/g,"-")}`,d=m?d:"number"==typeof d?d&&e in E?String(d)+"px":String(d):x(B(e,null==d?"":d),r.prefix,r.themeMap[e]),o[0].push(`${h?`${c} `:`${f(c)}:`}${d}`)}}var p,u};g(e),void 0!==o&&i(W(...o)),o=void 0};a(e,t,n)},W=(e,t,n)=>`${n.map((e=>`${e}{`)).join("")}${t.length?`${t.join(",")}{`:""}${e.join(";")}${t.length?"}":""}${Array(n.length?n.length+1:0).join("}")}`,E={animationDelay:1,animationDuration:1,backgroundSize:1,blockSize:1,border:1,borderBlock:1,borderBlockEnd:1,borderBlockEndWidth:1,borderBlockStart:1,borderBlockStartWidth:1,borderBlockWidth:1,borderBottom:1,borderBottomLeftRadius:1,borderBottomRightRadius:1,borderBottomWidth:1,borderEndEndRadius:1,borderEndStartRadius:1,borderInlineEnd:1,borderInlineEndWidth:1,borderInlineStart:1,borderInlineStartWidth:1,borderInlineWidth:1,borderLeft:1,borderLeftWidth:1,borderRadius:1,borderRight:1,borderRightWidth:1,borderSpacing:1,borderStartEndRadius:1,borderStartStartRadius:1,borderTop:1,borderTopLeftRadius:1,borderTopRightRadius:1,borderTopWidth:1,borderWidth:1,bottom:1,columnGap:1,columnRule:1,columnRuleWidth:1,columnWidth:1,containIntrinsicSize:1,flexBasis:1,fontSize:1,gap:1,gridAutoColumns:1,gridAutoRows:1,gridTemplateColumns:1,gridTemplateRows:1,height:1,inlineSize:1,inset:1,insetBlock:1,insetBlockEnd:1,insetBlockStart:1,insetInline:1,insetInlineEnd:1,insetInlineStart:1,left:1,letterSpacing:1,margin:1,marginBlock:1,marginBlockEnd:1,marginBlockStart:1,marginBottom:1,marginInline:1,marginInlineEnd:1,marginInlineStart:1,marginLeft:1,marginRight:1,marginTop:1,maxBlockSize:1,maxHeight:1,maxInlineSize:1,maxWidth:1,minBlockSize:1,minHeight:1,minInlineSize:1,minWidth:1,offsetDistance:1,offsetRotate:1,outline:1,outlineOffset:1,outlineWidth:1,overflowClipMargin:1,padding:1,paddingBlock:1,paddingBlockEnd:1,paddingBlockStart:1,paddingBottom:1,paddingInline:1,paddingInlineEnd:1,paddingInlineStart:1,paddingLeft:1,paddingRight:1,paddingTop:1,perspective:1,right:1,rowGap:1,scrollMargin:1,scrollMarginBlock:1,scrollMarginBlockEnd:1,scrollMarginBlockStart:1,scrollMarginBottom:1,scrollMarginInline:1,scrollMarginInlineEnd:1,scrollMarginInlineStart:1,scrollMarginLeft:1,scrollMarginRight:1,scrollMarginTop:1,scrollPadding:1,scrollPaddingBlock:1,scrollPaddingBlockEnd:1,scrollPaddingBlockStart:1,scrollPaddingBottom:1,scrollPaddingInline:1,scrollPaddingInlineEnd:1,scrollPaddingInlineStart:1,scrollPaddingLeft:1,scrollPaddingRight:1,scrollPaddingTop:1,shapeMargin:1,textDecoration:1,textDecorationThickness:1,textIndent:1,textUnderlineOffset:1,top:1,transitionDelay:1,transitionDuration:1,verticalAlign:1,width:1,wordSpacing:1},T=e=>String.fromCharCode(e+(e>25?39:97)),M=e=>(e=>{let t,n="";for(t=Math.abs(e);t>52;t=t/52|0)n=T(t%52)+n;return T(t%52)+n})(((e,t)=>{let n=t.length;for(;n;)e=33*e^t.charCodeAt(--n);return e})(5381,JSON.stringify(e))>>>0),v=["themed","global","styled","onevar","resonevar","allvar","inline"],w=e=>{if(e.href&&!e.href.startsWith(location.origin))return!1;try{return!!e.cssRules}catch(e){return!1}},C=e=>{let t;const n=()=>{const{cssRules:e}=t.sheet;return[].map.call(e,((n,r)=>{const{cssText:i}=n;let o="";if(i.startsWith("--sxs"))return"";if(e[r-1]&&(o=e[r-1].cssText).startsWith("--sxs")){if(!n.cssRules.length)return"";for(const e in t.rules)if(t.rules[e].group===n)return`--sxs{--sxs:${[...t.rules[e].cache].join(" ")}}${i}`;return n.cssRules.length?`${o}${i}`:""}return i})).join("")},r=()=>{if(t){const{rules:e,sheet:n}=t;if(!n.deleteRule){for(;3===Object(Object(n.cssRules)[0]).type;)n.cssRules.splice(0,1);n.cssRules=[]}for(const t in e)delete e[t]}const i=Object(e).styleSheets||[];for(const e of i)if(w(e)){for(let i=0,o=e.cssRules;o[i];++i){const l=Object(o[i]);if(1!==l.type)continue;const s=Object(o[i+1]);if(4!==s.type)continue;++i;const{cssText:a}=l;if(!a.startsWith("--sxs"))continue;const c=a.slice(14,-3).trim().split(/\s+/),d=v[c[0]];d&&(t||(t={sheet:e,reset:r,rules:{},toString:n}),t.rules[d]={group:s,index:i,cache:new Set(c)})}if(t)break}if(!t){const i=(e,t)=>({type:t,cssRules:[],insertRule(e,t){this.cssRules.splice(t,0,i(e,{import:3,undefined:1}[(e.toLowerCase().match(/^@([a-z]+)/)||[])[1]]||4))},get cssText(){return"@media{}"===e?`@media{${[].map.call(this.cssRules,(e=>e.cssText)).join("")}}`:e}});t={sheet:e?(e.head||e).appendChild(document.createElement("style")).sheet:i("","text/css"),rules:{},reset:r,toString:n}}const{sheet:o,rules:l}=t;for(let e=v.length-1;e>=0;--e){const t=v[e];if(!l[t]){const n=v[e+1],r=l[n]?l[n].index:o.cssRules.length;o.insertRule("@media{}",r),o.insertRule(`--sxs{--sxs:${e}}`,r),l[t]={group:o.cssRules[r+1],index:r,cache:new Set([e])}}P(l[t])}};return r(),t},P=e=>{const t=e.group;let n=t.cssRules.length;e.apply=e=>{try{t.insertRule(e,n),++n}catch(e){}}},L=Symbol(),O=d(),A=(e,t)=>O(e,(()=>(...n)=>{let r={type:null,composers:new Set};for(const t of n)if(null!=t)if(t[g]){null==r.type&&(r.type=t[g].type);for(const e of t[g].composers)r.composers.add(e)}else t.constructor!==Object||t.$$typeof?null==r.type&&(r.type=t):r.composers.add(N(t,e));return null==r.type&&(r.type="span"),r.composers.size||r.composers.add(["PJLV",{},[],[],{},[]]),D(e,r,t)})),N=({variants:e,compoundVariants:t,defaultVariants:n,...r},i)=>{const o=`${R(i.prefix)}c-${M(r)}`,l=[],s=[],a=Object.create(null),c=[];for(const e in n)a[e]=String(n[e]);if("object"==typeof e&&e)for(const t in e){d=a,g=t,h.call(d,g)||(a[t]="undefined");const n=e[t];for(const e in n){const r={[t]:String(e)};"undefined"===String(e)&&c.push(t);const i=n[e],o=[r,i,!u(i)];l.push(o)}}var d,g;if("object"==typeof t&&t)for(const e of t){let{css:t,...n}=e;t="object"==typeof t&&t||{};for(const e in n)n[e]=String(n[e]);const r=[n,t,!u(t)];s.push(r)}return[o,r,l,s,a,c]},D=(e,t,n)=>{const[r,i,o,l]=H(t.composers),s="function"==typeof t.type||t.type.$$typeof?(e=>{function t(){for(let n=0;n<t[L].length;n++){const[r,i]=t[L][n];e.rules[r].apply(i)}return t[L]=[],null}return t[L]=[],t.rules={},v.forEach((e=>t.rules[e]={apply:n=>t[L].push([e,n])})),t})(n):null,a=(s||n).rules,c=`.${r}${i.length>1?`:where(.${i.slice(1).join(".")})`:""}`,d=d=>{d="object"==typeof d&&d||G;const{css:g,...p}=d,u={};for(const e in o)if(delete p[e],e in d){let t=d[e];"object"==typeof t&&t?u[e]={"@initial":o[e],...t}:(t=String(t),u[e]="undefined"!==t||l.has(e)?t:o[e])}else u[e]=o[e];const h=new Set([...i]);for(const[r,i,o,l]of t.composers){n.rules.styled.cache.has(r)||(n.rules.styled.cache.add(r),j(i,[`.${r}`],[],e,(e=>{a.styled.apply(e)})));const t=V(o,u,e.media),s=V(l,u,e.media,!0);for(const i of t)if(void 0!==i)for(const[t,o,l]of i){const i=`${r}-${M(o)}-${t}`;h.add(i);const s=(l?n.rules.resonevar:n.rules.onevar).cache,c=l?a.resonevar:a.onevar;s.has(i)||(s.add(i),j(o,[`.${i}`],[],e,(e=>{c.apply(e)})))}for(const t of s)if(void 0!==t)for(const[i,o]of t){const t=`${r}-${M(o)}-${i}`;h.add(t),n.rules.allvar.cache.has(t)||(n.rules.allvar.cache.add(t),j(o,[`.${t}`],[],e,(e=>{a.allvar.apply(e)})))}}if("object"==typeof g&&g){const t=`${r}-i${M(g)}-css`;h.add(t),n.rules.inline.cache.has(t)||(n.rules.inline.cache.add(t),j(g,[`.${t}`],[],e,(e=>{a.inline.apply(e)})))}for(const e of String(d.className||"").trim().split(/\s+/))e&&h.add(e);const f=p.className=[...h].join(" ");return{type:t.type,className:f,selector:c,props:p,toString:()=>f,deferredInjector:s}};return p(d,{className:r,selector:c,[g]:t,toString:()=>(n.rules.styled.cache.has(r)||d(),r)})},H=e=>{let t="";const n=[],r={},i=[];for(const[o,,,,l,s]of e){""===t&&(t=o),n.push(o),i.push(...s);for(const e in l){const t=l[e];(void 0===r[e]||"undefined"!==t||s.includes(t))&&(r[e]=t)}}return[t,n,r,new Set(i)]},V=(e,t,n,r)=>{const i=[];e:for(let[o,l,s]of e){if(s)continue;let e,a=0,c=!1;for(e in o){const r=o[e];let i=t[e];if(i!==r){if("object"!=typeof i||!i)continue e;{let e,t,o=0;for(const l in i){if(r===String(i[l])){if("@initial"!==l){const e=l.slice(1);(t=t||[]).push(e in n?n[e]:l.replace(/^@media ?/,"")),c=!0}a+=o,e=!0}++o}if(t&&t.length&&(l={["@media "+t.join(", ")]:l}),!e)continue e}}}(i[a]=i[a]||[]).push([r?"cv":`${e}-${o[e]}`,l,c])}return i},G={},F=d(),J=(e,t)=>F(e,(()=>(...n)=>{const r=()=>{for(let r of n){r="object"==typeof r&&r||{};let n=M(r);if(!t.rules.global.cache.has(n)){if(t.rules.global.cache.add(n),"@import"in r){let e=[].indexOf.call(t.sheet.cssRules,t.rules.themed.group)-1;for(let n of[].concat(r["@import"]))n=n.includes('"')||n.includes("'")?n:`"${n}"`,t.sheet.insertRule(`@import ${n};`,e++);delete r["@import"]}j(r,[],[],e,(e=>{t.rules.global.apply(e)}))}}return""};return p(r,{toString:r})})),U=d(),Z=(e,t)=>U(e,(()=>n=>{const r=`${R(e.prefix)}k-${M(n)}`,i=()=>{if(!t.rules.global.cache.has(r)){t.rules.global.cache.add(r);const i=[];j(n,[],[],e,(e=>i.push(e)));const o=`@keyframes ${r}{${i.join("")}}`;t.rules.global.apply(o)}return r};return p(i,{get name(){return i()},toString:i})})),X=class{constructor(e,t,n,r){this.token=null==e?"":String(e),this.value=null==t?"":String(t),this.scale=null==n?"":String(n),this.prefix=null==r?"":String(r)}get computedValue(){return"var("+this.variable+")"}get variable(){return"--"+R(this.prefix)+R(this.scale)+this.token}toString(){return this.computedValue}},Y=d(),q=(e,t)=>Y(e,(()=>(n,r)=>{r="object"==typeof n&&n||Object(r);const i=`.${n=(n="string"==typeof n?n:"")||`${R(e.prefix)}t-${M(r)}`}`,o={},l=[];for(const t in r){o[t]={};for(const n in r[t]){const i=`--${R(e.prefix)}${t}-${n}`,s=x(String(r[t][n]),e.prefix,t);o[t][n]=new X(n,s,t,e.prefix),l.push(`${i}:${s}`)}}const s=()=>{if(l.length&&!t.rules.themed.cache.has(n)){t.rules.themed.cache.add(n);const i=`${r===e.theme?":root,":""}.${n}{${l.join(";")}}`;t.rules.themed.apply(i)}return n};return{...o,get className(){return s()},selector:i,toString:s}})),K=d(),Q=d(),_=e=>{const t=(e=>{let t=!1;const n=K(e,(e=>{t=!0;const n="prefix"in(e="object"==typeof e&&e||{})?String(e.prefix):"",r="object"==typeof e.media&&e.media||{},i="object"==typeof e.root?e.root||null:globalThis.document||null,o="object"==typeof e.theme&&e.theme||{},l={prefix:n,media:r,theme:o,themeMap:"object"==typeof e.themeMap&&e.themeMap||{...a},utils:"object"==typeof e.utils&&e.utils||{}},s=C(i),c={css:A(l,s),globalCss:J(l,s),keyframes:Z(l,s),createTheme:q(l,s),reset(){s.reset(),c.theme.toString()},theme:{},sheet:s,config:l,prefix:n,getCssText:s.toString,toString:s.toString};return String(c.theme=c.createTheme(o)),c}));return t||n.reset(),n})(e);return t.styled=(({config:e,sheet:t})=>Q(e,(()=>{const n=A(e,t);return(...e)=>{const t=n(...e),r=t[g].type,i=React.forwardRef(((e,n)=>{const i=e&&e.as||r,{props:o,deferredInjector:l}=t(e);return delete o.as,o.ref=n,l?React.createElement(React.Fragment,null,React.createElement(i,o),React.createElement(l,null)):React.createElement(i,o)}));return i.className=t.className,i.displayName=`Styled.${r.displayName||r.name||r}`,i.selector=t.selector,i.toString=()=>t.selector,i[g]=t[g],i}})))(t),t},ee=()=>e||(e=_());return{createStitches:_,createTheme:(...e)=>ee().createTheme(...e),css:(...e)=>ee().css(...e),defaultThemeMap:a,globalCss:(...e)=>ee().globalCss(...e),keyframes:(...e)=>ee().keyframes(...e),styled:(...e)=>ee().styled(...e)}})();
//# sourceMappingUrl=index.map