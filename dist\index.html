<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/terminal-icon.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="Professional Terminal Portfolio - Interactive developer portfolio with terminal interface" />
    <meta name="keywords" content="portfolio, developer, terminal, react, typescript" />
    <title>Terminal Portfolio</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }
      
      body {
        font-family: 'Fira Code', 'Consolas', 'Monaco', monospace;
        background: #0a0a0a;
        overflow: hidden;
      }
      
      #root {
        width: 100vw;
        height: 100vh;
      }
    </style>
    <script type="module" crossorigin src="/assets/index-DiKvYFGD.js"></script>
  </head>
  <body>
    <div id="root"></div>
  </body>
</html>
