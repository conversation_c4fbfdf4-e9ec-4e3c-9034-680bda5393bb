import React, { useRef, useState, useEffect } from 'react';
import { Canvas, useFrame, useThree } from '@react-three/fiber';
import { Text, Box } from '@react-three/drei';
import * as THREE from 'three';
import styled from 'styled-components';
import { personalInfo, contactInfo } from '../../data/portfolio';

const CanvasContainer = styled.div`
  width: 100%;
  height: 100%;
  position: relative;
  background: transparent;
`;

interface CardProps {
  position: [number, number, number];
  isFlipped: boolean;
  onFlip: () => void;
}

const PhysicsCard: React.FC<CardProps> = ({ position, isFlipped, onFlip }) => {
  const meshRef = useRef<THREE.Group>(null);
  const { mouse } = useThree();
  
  // Physics simulation variables
  const velocity = useRef(new THREE.Vector3(0, 0, 0));
  const acceleration = useRef(new THREE.Vector3(0, 0, 0));
  const targetRotation = useRef(new THREE.Euler(0, 0, 0));
  const currentRotation = useRef(new THREE.Euler(0, 0, 0));
  
  // Mouse interaction
  const [isDragging, setIsDragging] = useState(false);
  const lastMousePos = useRef(new THREE.Vector2(0, 0));

  useFrame((state, delta) => {
    if (!meshRef.current) return;

    const group = meshRef.current;
    const time = state.clock.elapsedTime;

    // Enhanced physics simulation
    const gravity = new THREE.Vector3(0, -9.81 * 0.1, 0);
    const restPosition = new THREE.Vector3(0, 0, 0);
    const currentPos = group.position.clone();

    // Spring force (restoring force towards center)
    const springForce = restPosition.clone().sub(currentPos).multiplyScalar(2.0);

    // Damping force (air resistance)
    const dampingForce = velocity.current.clone().multiplyScalar(-0.98);

    // Mouse interaction with more realistic physics
    if (isDragging) {
      const mouseDelta = new THREE.Vector3(
        (mouse.x - lastMousePos.current.x) * 5,
        -(mouse.y - lastMousePos.current.y) * 5,
        0
      );
      acceleration.current.add(mouseDelta);
    }

    // Add subtle wind effect for natural movement
    const windForce = new THREE.Vector3(
      Math.sin(time * 0.5) * 0.1,
      Math.cos(time * 0.3) * 0.05,
      Math.sin(time * 0.7) * 0.02
    );

    // Combine all forces
    acceleration.current.add(gravity).add(springForce).add(dampingForce).add(windForce);

    // Update velocity and position
    velocity.current.add(acceleration.current.clone().multiplyScalar(delta));
    const newPosition = currentPos.add(velocity.current.clone().multiplyScalar(delta));

    // Constrain movement (hanging effect)
    const maxDistance = 1.5;
    if (newPosition.length() > maxDistance) {
      newPosition.normalize().multiplyScalar(maxDistance);
      velocity.current.multiplyScalar(0.8); // Energy loss on constraint
    }

    group.position.copy(newPosition);

    // Realistic rotation based on physics
    const rotationDamping = 0.95;
    targetRotation.current.z = velocity.current.x * 0.5 + Math.sin(time * 0.2) * 0.1;
    targetRotation.current.x = velocity.current.y * 0.3 + Math.cos(time * 0.15) * 0.05;

    // Smooth rotation with physics-based interpolation
    currentRotation.current.x += (targetRotation.current.x - currentRotation.current.x) * 0.15;
    currentRotation.current.z += (targetRotation.current.z - currentRotation.current.z) * 0.15;

    // Apply rotation damping
    currentRotation.current.x *= rotationDamping;
    currentRotation.current.z *= rotationDamping;

    // Flip animation
    const flipSpeed = 0.12;
    if (isFlipped) {
      currentRotation.current.y += (Math.PI - currentRotation.current.y) * flipSpeed;
    } else {
      currentRotation.current.y += (0 - currentRotation.current.y) * flipSpeed;
    }

    group.rotation.copy(currentRotation.current);

    // Reset acceleration for next frame
    acceleration.current.set(0, 0, 0);
    lastMousePos.current.set(mouse.x, mouse.y);
  });

  const handlePointerDown = (event: any) => {
    event.stopPropagation();
    setIsDragging(true);
  };

  const handlePointerUp = () => {
    setIsDragging(false);
  };

  const handleClick = (event: any) => {
    event.stopPropagation();
    onFlip();
  };

  useEffect(() => {
    const handleMouseUp = () => setIsDragging(false);
    window.addEventListener('mouseup', handleMouseUp);
    return () => window.removeEventListener('mouseup', handleMouseUp);
  }, []);

  return (
    <group
      ref={meshRef}
      position={position}
      onPointerDown={handlePointerDown}
      onPointerUp={handlePointerUp}
      onClick={handleClick}
    >
      {/* Hanging String */}
      <mesh position={[0, 2, 0]}>
        <cylinderGeometry args={[0.01, 0.01, 4]} />
        <meshStandardMaterial color="#666666" />
      </mesh>
      
      {/* Card Front */}
      <group visible={!isFlipped}>
        <Box args={[3, 4, 0.1]} position={[0, 0, 0]}>
          <meshStandardMaterial
            color="#1a1a2e"
            metalness={0.3}
            roughness={0.4}
          />
        </Box>
        
        {/* Profile Image Circle */}
        <mesh position={[0, 1.2, 0.06]}>
          <circleGeometry args={[0.6]} />
          <meshStandardMaterial
            color="#00ffff"
            metalness={0.5}
            roughness={0.3}
          />
        </mesh>
        
        {/* Profile Emoji */}
        <Text
          position={[0, 1.2, 0.07]}
          fontSize={0.8}
          color="#ffffff"
          anchorX="center"
          anchorY="middle"
        >
          👨‍💻
        </Text>
        
        {/* Name */}
        <Text
          position={[0, 0.3, 0.06]}
          fontSize={0.25}
          color="#ffffff"
          anchorX="center"
          anchorY="middle"
          maxWidth={2.5}
        >
          {personalInfo.name}
        </Text>
        
        {/* Title */}
        <Text
          position={[0, -0.1, 0.06]}
          fontSize={0.18}
          color="#00ffff"
          anchorX="center"
          anchorY="middle"
          maxWidth={2.5}
        >
          {personalInfo.title}
        </Text>
        
        {/* Bio */}
        <Text
          position={[0, -0.7, 0.06]}
          fontSize={0.12}
          color="#cccccc"
          anchorX="center"
          anchorY="middle"
          maxWidth={2.3}
          lineHeight={1.2}
        >
          {personalInfo.bio}
        </Text>
        
        {/* Experience */}
        <Text
          position={[0, -1.4, 0.06]}
          fontSize={0.15}
          color="#ffff00"
          anchorX="center"
          anchorY="middle"
        >
          {personalInfo.yearsOfExperience}+ Years Experience
        </Text>
        
        {/* Flip Indicator */}
        <Text
          position={[1.2, -1.7, 0.06]}
          fontSize={0.1}
          color="#00ffff"
          anchorX="center"
          anchorY="middle"
        >
          Click to flip →
        </Text>
      </group>
      
      {/* Card Back */}
      <group visible={isFlipped}>
        <Box args={[3, 4, 0.1]} position={[0, 0, 0]}>
          <meshStandardMaterial
            color="#2a1a3e"
            metalness={0.3}
            roughness={0.4}
          />
        </Box>
        
        {/* Contact Title */}
        <Text
          position={[0, 1.5, 0.06]}
          fontSize={0.3}
          color="#ffffff"
          anchorX="center"
          anchorY="middle"
        >
          Contact Info
        </Text>
        
        {/* Email */}
        <Text
          position={[0, 0.9, 0.06]}
          fontSize={0.12}
          color="#00ffff"
          anchorX="center"
          anchorY="middle"
        >
          Email
        </Text>
        <Text
          position={[0, 0.7, 0.06]}
          fontSize={0.1}
          color="#ffffff"
          anchorX="center"
          anchorY="middle"
          maxWidth={2.5}
        >
          {contactInfo.email}
        </Text>
        
        {/* GitHub */}
        <Text
          position={[0, 0.3, 0.06]}
          fontSize={0.12}
          color="#00ffff"
          anchorX="center"
          anchorY="middle"
        >
          GitHub
        </Text>
        <Text
          position={[0, 0.1, 0.06]}
          fontSize={0.1}
          color="#ffffff"
          anchorX="center"
          anchorY="middle"
          maxWidth={2.5}
        >
          {contactInfo.github.replace('https://', '')}
        </Text>
        
        {/* LinkedIn */}
        <Text
          position={[0, -0.3, 0.06]}
          fontSize={0.12}
          color="#00ffff"
          anchorX="center"
          anchorY="middle"
        >
          LinkedIn
        </Text>
        <Text
          position={[0, -0.5, 0.06]}
          fontSize={0.1}
          color="#ffffff"
          anchorX="center"
          anchorY="middle"
          maxWidth={2.5}
        >
          {contactInfo.linkedin.replace('https://', '')}
        </Text>
        
        {/* Location */}
        <Text
          position={[0, -0.9, 0.06]}
          fontSize={0.12}
          color="#00ffff"
          anchorX="center"
          anchorY="middle"
        >
          Location
        </Text>
        <Text
          position={[0, -1.1, 0.06]}
          fontSize={0.1}
          color="#ffffff"
          anchorX="center"
          anchorY="middle"
        >
          {contactInfo.location}
        </Text>
        
        {/* Flip Indicator */}
        <Text
          position={[-1.2, -1.7, 0.06]}
          fontSize={0.1}
          color="#00ffff"
          anchorX="center"
          anchorY="middle"
        >
          ← Click to flip
        </Text>
      </group>
      
      {/* Card Border Glow */}
      <mesh position={[0, 0, -0.01]}>
        <planeGeometry args={[3.1, 4.1]} />
        <meshStandardMaterial
          color="#00ffff"
          transparent
          opacity={0.2}
          emissive="#00ffff"
          emissiveIntensity={0.1}
        />
      </mesh>
    </group>
  );
};

export const PhysicsCardCanvas: React.FC = () => {
  const [isFlipped, setIsFlipped] = useState(false);

  const handleFlip = () => {
    setIsFlipped(!isFlipped);
  };

  return (
    <CanvasContainer>
      <Canvas
        camera={{ position: [0, 0, 8], fov: 50 }}
        style={{ background: 'transparent' }}
      >
        {/* Lighting */}
        <ambientLight intensity={0.4} />
        <pointLight position={[10, 10, 10]} intensity={1} />
        <pointLight position={[-10, -10, -10]} intensity={0.5} color="#00ffff" />
        <spotLight
          position={[0, 10, 5]}
          angle={0.3}
          penumbra={1}
          intensity={0.8}
          castShadow
        />
        
        {/* Physics Card */}
        <PhysicsCard
          position={[0, 0, 0]}
          isFlipped={isFlipped}
          onFlip={handleFlip}
        />
        
        {/* Animated Background Particles */}
        {Array.from({ length: 50 }).map((_, i) => {
          const ParticleComponent = () => {
            const particleRef = useRef<THREE.Mesh>(null);

            useFrame((state) => {
              if (particleRef.current) {
                const time = state.clock.elapsedTime;
                particleRef.current.position.y += Math.sin(time + i) * 0.001;
                particleRef.current.position.x += Math.cos(time + i * 0.5) * 0.001;
                particleRef.current.rotation.z += 0.01;

                // Fade in and out
                const opacity = (Math.sin(time * 0.5 + i) + 1) * 0.15;
                if (particleRef.current.material instanceof THREE.MeshStandardMaterial) {
                  particleRef.current.material.opacity = opacity;
                }
              }
            });

            return (
              <mesh
                ref={particleRef}
                position={[
                  (Math.random() - 0.5) * 20,
                  (Math.random() - 0.5) * 20,
                  (Math.random() - 0.5) * 20
                ]}
              >
                <sphereGeometry args={[0.02]} />
                <meshStandardMaterial
                  color="#00ffff"
                  transparent
                  opacity={0.3}
                  emissive="#00ffff"
                  emissiveIntensity={0.2}
                />
              </mesh>
            );
          };

          return <ParticleComponent key={i} />;
        })}

        {/* Floating Code Symbols */}
        {['<', '>', '{', '}', '(', ')', ';', '='].map((symbol, i) => {
          const SymbolComponent = () => {
            const symbolRef = useRef<THREE.Group>(null);

            useFrame((state) => {
              if (symbolRef.current) {
                const time = state.clock.elapsedTime;
                symbolRef.current.position.y += Math.sin(time * 0.3 + i) * 0.002;
                symbolRef.current.rotation.y += 0.005;
              }
            });

            return (
              <group
                ref={symbolRef}
                position={[
                  (Math.random() - 0.5) * 15,
                  (Math.random() - 0.5) * 15,
                  (Math.random() - 0.5) * 10
                ]}
              >
                <Text
                  fontSize={0.3}
                  color="#00ffff"
                  fillOpacity={0.1}
                >
                  {symbol}
                </Text>
              </group>
            );
          };

          return <SymbolComponent key={symbol + i} />;
        })}
      </Canvas>
    </CanvasContainer>
  );
};
