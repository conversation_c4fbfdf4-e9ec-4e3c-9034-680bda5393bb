import { Command, TerminalOutput } from '../types/terminal';
import { personalInfo, skills, projects, experience, education, contactInfo } from '../data/portfolio';

const createOutput = (content: string, type: 'text' | 'html' | 'error' | 'success' | 'warning' = 'text'): TerminalOutput => ({
  type,
  content
});

const formatProjects = (): string => {
  const featuredProjects = projects.filter(p => p.featured);
  const otherProjects = projects.filter(p => !p.featured);
  
  let output = '<div style="color: #00ff00; font-weight: bold;">📁 FEATURED PROJECTS</div>\n\n';
  
  featuredProjects.forEach((project, index) => {
    output += `<div style="color: #ffff00;">${index + 1}. ${project.name}</div>`;
    output += `<div style="color: #ffffff; margin-left: 20px;">${project.description}</div>`;
    output += `<div style="color: #00ffff; margin-left: 20px;">Tech: ${project.technologies.join(', ')}</div>`;
    if (project.githubUrl) output += `<div style="color: #ff00ff; margin-left: 20px;">GitHub: ${project.githubUrl}</div>`;
    if (project.liveUrl) output += `<div style="color: #ff00ff; margin-left: 20px;">Live: ${project.liveUrl}</div>`;
    output += '\n';
  });

  if (otherProjects.length > 0) {
    output += '<div style="color: #00ff00; font-weight: bold;">📂 OTHER PROJECTS</div>\n\n';
    otherProjects.forEach((project, index) => {
      output += `<div style="color: #ffff00;">${index + 1}. ${project.name}</div>`;
      output += `<div style="color: #ffffff; margin-left: 20px;">${project.description}</div>`;
      output += `<div style="color: #00ffff; margin-left: 20px;">Tech: ${project.technologies.join(', ')}</div>`;
      if (project.githubUrl) output += `<div style="color: #ff00ff; margin-left: 20px;">GitHub: ${project.githubUrl}</div>`;
      output += '\n';
    });
  }

  return output;
};

const formatSkills = (): string => {
  let output = '<div style="color: #00ff00; font-weight: bold;">🛠️ TECHNICAL SKILLS</div>\n\n';
  
  Object.entries(skills).forEach(([category, skillList]) => {
    const categoryName = category.charAt(0).toUpperCase() + category.slice(1);
    output += `<div style="color: #ffff00;">${categoryName}:</div>`;
    output += `<div style="color: #ffffff; margin-left: 20px;">${skillList.join(' • ')}</div>\n`;
  });

  return output;
};

const formatExperience = (): string => {
  let output = '<div style="color: #00ff00; font-weight: bold;">💼 WORK EXPERIENCE</div>\n\n';
  
  experience.forEach((exp, index) => {
    output += `<div style="color: #ffff00;">${index + 1}. ${exp.position} at ${exp.company}</div>`;
    output += `<div style="color: #00ffff; margin-left: 20px;">Duration: ${exp.duration}</div>`;
    output += `<div style="color: #ffffff; margin-left: 20px;">${exp.description}</div>`;
    output += `<div style="color: #ff00ff; margin-left: 20px;">Technologies: ${exp.technologies.join(', ')}</div>\n`;
  });

  return output;
};

const formatEducation = (): string => {
  let output = '<div style="color: #00ff00; font-weight: bold;">🎓 EDUCATION</div>\n\n';
  
  education.forEach((edu, index) => {
    output += `<div style="color: #ffff00;">${index + 1}. ${edu.degree}</div>`;
    output += `<div style="color: #00ffff; margin-left: 20px;">Institution: ${edu.institution}</div>`;
    output += `<div style="color: #ffffff; margin-left: 20px;">Duration: ${edu.duration}</div>`;
    if (edu.description) output += `<div style="color: #ffffff; margin-left: 20px;">${edu.description}</div>`;
    output += '\n';
  });

  return output;
};

const formatContact = (): string => {
  let output = '<div style="color: #00ff00; font-weight: bold;">📞 CONTACT INFORMATION</div>\n\n';
  
  output += `<div style="color: #ffff00;">Email:</div>`;
  output += `<div style="color: #ffffff; margin-left: 20px;">${contactInfo.email}</div>\n`;
  
  if (contactInfo.phone) {
    output += `<div style="color: #ffff00;">Phone:</div>`;
    output += `<div style="color: #ffffff; margin-left: 20px;">${contactInfo.phone}</div>\n`;
  }
  
  output += `<div style="color: #ffff00;">Location:</div>`;
  output += `<div style="color: #ffffff; margin-left: 20px;">${contactInfo.location}</div>\n`;
  
  output += `<div style="color: #ffff00;">LinkedIn:</div>`;
  output += `<div style="color: #ff00ff; margin-left: 20px;">${contactInfo.linkedin}</div>\n`;
  
  output += `<div style="color: #ffff00;">GitHub:</div>`;
  output += `<div style="color: #ff00ff; margin-left: 20px;">${contactInfo.github}</div>\n`;
  
  if (contactInfo.website) {
    output += `<div style="color: #ffff00;">Website:</div>`;
    output += `<div style="color: #ff00ff; margin-left: 20px;">${contactInfo.website}</div>\n`;
  }

  return output;
};

export const commands: Record<string, Command> = {
  help: {
    name: 'help',
    description: 'Show all available commands',
    execute: () => {
      const portfolioCommands = ['about', 'projects', 'skills', 'experience', 'education', 'contact', 'resume', 'github'];
      const systemCommands = ['clear', 'history', 'ls', 'pwd', 'whoami', 'date', 'echo'];
      const funCommands = ['neofetch', 'matrix', 'hack', 'fortune', 'joke', 'coffee', 'music', 'ascii', 'weather'];
      const themeCommands = ['themes', 'theme'];

      return createOutput(`
<div style="color: #00ff00; font-weight: bold;">🚀 TERMINAL PORTFOLIO - COMMAND REFERENCE</div>

<div style="color: #ffff00; font-weight: bold;">📁 PORTFOLIO COMMANDS</div>
${portfolioCommands.map(cmd => `<div style="color: #00ffff;">${cmd.padEnd(12)}</div><div style="color: #ffffff;">${commands[cmd]?.description || ''}</div>`).join('\n')}

<div style="color: #ffff00; font-weight: bold;">⚙️ SYSTEM COMMANDS</div>
${systemCommands.map(cmd => `<div style="color: #00ffff;">${cmd.padEnd(12)}</div><div style="color: #ffffff;">${commands[cmd]?.description || ''}</div>`).join('\n')}

<div style="color: #ffff00; font-weight: bold;">🎨 THEME COMMANDS</div>
${themeCommands.map(cmd => `<div style="color: #00ffff;">${cmd.padEnd(12)}</div><div style="color: #ffffff;">${commands[cmd]?.description || ''}</div>`).join('\n')}

<div style="color: #ffff00; font-weight: bold;">🎮 FUN COMMANDS</div>
${funCommands.map(cmd => `<div style="color: #00ffff;">${cmd.padEnd(12)}</div><div style="color: #ffffff;">${commands[cmd]?.description || ''}</div>`).join('\n')}

<div style="color: #ffff00; margin-top: 20px;">💡 TIPS & SHORTCUTS:</div>
<div style="color: #ffffff;">• Use TAB for autocompletion</div>
<div style="color: #ffffff;">• Use ↑/↓ arrows for command history</div>
<div style="color: #ffffff;">• Drag the 3D card on the left!</div>
<div style="color: #ffffff;">• Try 'matrix' for a cool theme change</div>
<div style="color: #00ffff;">• Type any command name for instant execution</div>
      `, 'html');
    }
  },

  about: {
    name: 'about',
    description: 'Learn more about me',
    execute: () => {
      return createOutput(`
<div style="color: #00ff00; font-weight: bold;">👨‍💻 ABOUT ME</div>

<div style="color: #ffff00;">Name:</div>
<div style="color: #ffffff; margin-left: 20px;">${personalInfo.name}</div>

<div style="color: #ffff00;">Title:</div>
<div style="color: #ffffff; margin-left: 20px;">${personalInfo.title}</div>

<div style="color: #ffff00;">Experience:</div>
<div style="color: #ffffff; margin-left: 20px;">${personalInfo.yearsOfExperience}+ years</div>

<div style="color: #ffff00;">Location:</div>
<div style="color: #ffffff; margin-left: 20px;">${personalInfo.location}</div>

<div style="color: #ffff00;">Bio:</div>
<div style="color: #ffffff; margin-left: 20px;">${personalInfo.bio}</div>
      `, 'html');
    }
  },

  projects: {
    name: 'projects',
    description: 'View my portfolio projects',
    execute: () => createOutput(formatProjects(), 'html')
  },

  skills: {
    name: 'skills',
    description: 'Display my technical skills',
    aliases: ['tech'],
    execute: () => createOutput(formatSkills(), 'html')
  },

  experience: {
    name: 'experience',
    description: 'Show my work experience',
    aliases: ['work'],
    execute: () => createOutput(formatExperience(), 'html')
  },

  education: {
    name: 'education',
    description: 'Display my educational background',
    execute: () => createOutput(formatEducation(), 'html')
  },

  contact: {
    name: 'contact',
    description: 'Get my contact information',
    execute: () => createOutput(formatContact(), 'html')
  },

  github: {
    name: 'github',
    description: 'Open my GitHub profile',
    execute: () => {
      window.open(contactInfo.github, '_blank');
      return createOutput(`Opening GitHub profile: ${contactInfo.github}`, 'success');
    }
  },

  resume: {
    name: 'resume',
    description: 'Download my resume',
    execute: () => {
      // Create a download link for resume
      const link = document.createElement('a');
      link.href = '/resume.pdf';
      link.download = 'resume.pdf';
      link.click();
      return createOutput('Downloading resume...', 'success');
    }
  },

  clear: {
    name: 'clear',
    description: 'Clear the terminal screen',
    execute: () => createOutput('CLEAR_TERMINAL', 'text')
  },

  echo: {
    name: 'echo',
    description: 'Display a line of text',
    usage: 'echo [text]',
    execute: (args: string[]) => {
      const text = args.join(' ');
      return createOutput(text || '', 'text');
    }
  },

  ls: {
    name: 'ls',
    description: 'List directory contents',
    execute: () => {
      return createOutput(`
<div style="color: #00ffff;">total 8</div>
<div style="color: #ffff00;">drwxr-xr-x  2 <USER> <GROUP> 4096 ${new Date().toLocaleDateString()} about/</div>
<div style="color: #ffff00;">drwxr-xr-x  2 <USER> <GROUP> 4096 ${new Date().toLocaleDateString()} projects/</div>
<div style="color: #ffff00;">drwxr-xr-x  2 <USER> <GROUP> 4096 ${new Date().toLocaleDateString()} skills/</div>
<div style="color: #ffff00;">drwxr-xr-x  2 <USER> <GROUP> 4096 ${new Date().toLocaleDateString()} experience/</div>
<div style="color: #ffffff;">-rw-r--r--  1 <USER> <GROUP> 2048 ${new Date().toLocaleDateString()} resume.pdf</div>
<div style="color: #ffffff;">-rw-r--r--  1 <USER> <GROUP> 1024 ${new Date().toLocaleDateString()} contact.txt</div>
      `, 'html');
    }
  },

  pwd: {
    name: 'pwd',
    description: 'Print working directory',
    execute: () => createOutput('/home/<USER>', 'text')
  },

  whoami: {
    name: 'whoami',
    description: 'Display current user',
    execute: () => createOutput(personalInfo.name.toLowerCase().replace(' ', ''), 'text')
  },

  date: {
    name: 'date',
    description: 'Display current date and time',
    execute: () => createOutput(new Date().toString(), 'text')
  },

  themes: {
    name: 'themes',
    description: 'List available terminal themes',
    execute: () => {
      return createOutput(`
<div style="color: #00ff00; font-weight: bold;">🎨 AVAILABLE THEMES</div>

<div style="color: #ffff00;">dark</div><div style="color: #ffffff; margin-left: 20px;">Default dark theme</div>
<div style="color: #ffff00;">matrix</div><div style="color: #ffffff; margin-left: 20px;">Green matrix style</div>
<div style="color: #ffff00;">cyberpunk</div><div style="color: #ffffff; margin-left: 20px;">Neon cyberpunk theme</div>
<div style="color: #ffff00;">ubuntu</div><div style="color: #ffffff; margin-left: 20px;">Ubuntu terminal theme</div>
<div style="color: #ffff00;">dracula</div><div style="color: #ffffff; margin-left: 20px;">Dracula color scheme</div>

<div style="color: #00ffff; margin-top: 20px;">Usage: theme [theme-name]</div>
      `, 'html');
    }
  },

  theme: {
    name: 'theme',
    description: 'Change terminal theme',
    usage: 'theme [theme-name]',
    execute: (args: string[]) => {
      const themeName = args[0];
      if (!themeName) {
        return createOutput('Usage: theme [theme-name]\nType "themes" to see available themes.', 'error');
      }

      const availableThemes = ['dark', 'matrix', 'cyberpunk', 'ubuntu', 'dracula'];
      if (!availableThemes.includes(themeName)) {
        return createOutput(`Theme "${themeName}" not found. Type "themes" to see available themes.`, 'error');
      }

      return createOutput(`CHANGE_THEME:${themeName}`, 'success');
    }
  },

  history: {
    name: 'history',
    description: 'Show command history',
    execute: () => createOutput('SHOW_HISTORY', 'text')
  },

  cat: {
    name: 'cat',
    description: 'Display file contents',
    usage: 'cat [filename]',
    execute: (args: string[]) => {
      const filename = args[0];
      if (!filename) {
        return createOutput('Usage: cat [filename]\nAvailable files: about.txt, contact.txt, skills.txt', 'error');
      }

      switch (filename) {
        case 'about.txt':
          return createOutput(`${personalInfo.name}\n${personalInfo.title}\n\n${personalInfo.bio}`, 'text');
        case 'contact.txt':
          return createOutput(`Email: ${contactInfo.email}\nGitHub: ${contactInfo.github}\nLinkedIn: ${contactInfo.linkedin}`, 'text');
        case 'skills.txt':
          return createOutput(`Languages: ${skills.languages.join(', ')}\nFrontend: ${skills.frontend.join(', ')}\nBackend: ${skills.backend.join(', ')}`, 'text');
        default:
          return createOutput(`cat: ${filename}: No such file or directory`, 'error');
      }
    }
  },

  tree: {
    name: 'tree',
    description: 'Display directory tree structure',
    execute: () => {
      return createOutput(`
<div style="color: #00ffff;">portfolio/</div>
<div style="color: #ffffff;">├── about/</div>
<div style="color: #ffffff;">│   ├── bio.txt</div>
<div style="color: #ffffff;">│   └── experience.txt</div>
<div style="color: #ffffff;">├── projects/</div>
<div style="color: #ffffff;">│   ├── featured/</div>
<div style="color: #ffffff;">│   └── archive/</div>
<div style="color: #ffffff;">├── skills/</div>
<div style="color: #ffffff;">│   ├── languages.txt</div>
<div style="color: #ffffff;">│   ├── frameworks.txt</div>
<div style="color: #ffffff;">│   └── tools.txt</div>
<div style="color: #ffffff;">├── contact.txt</div>
<div style="color: #ffffff;">└── resume.pdf</div>
      `, 'html');
    }
  },

  neofetch: {
    name: 'neofetch',
    description: 'Display system information',
    execute: () => {
      return createOutput(`
<div style="color: #00ff00; font-weight: bold;">                    ${personalInfo.name}</div>
<div style="color: #00ff00; font-weight: bold;">                    ${'─'.repeat(personalInfo.name.length)}</div>
<div style="color: #ffff00;">OS:</div><div style="color: #ffffff; margin-left: 20px;">Portfolio Terminal v1.0</div>
<div style="color: #ffff00;">Host:</div><div style="color: #ffffff; margin-left: 20px;">${personalInfo.title}</div>
<div style="color: #ffff00;">Kernel:</div><div style="color: #ffffff; margin-left: 20px;">React 18.x</div>
<div style="color: #ffff00;">Uptime:</div><div style="color: #ffffff; margin-left: 20px;">${personalInfo.yearsOfExperience}+ years experience</div>
<div style="color: #ffff00;">Shell:</div><div style="color: #ffffff; margin-left: 20px;">portfolio-terminal</div>
<div style="color: #ffff00;">Terminal:</div><div style="color: #ffffff; margin-left: 20px;">Interactive Portfolio</div>
<div style="color: #ffff00;">CPU:</div><div style="color: #ffffff; margin-left: 20px;">Problem Solving Unit</div>
<div style="color: #ffff00;">Memory:</div><div style="color: #ffffff; margin-left: 20px;">Unlimited creativity</div>
<div style="color: #ffff00;">Location:</div><div style="color: #ffffff; margin-left: 20px;">${personalInfo.location}</div>

<div style="color: #ff0000;">██</div><div style="color: #00ff00;">██</div><div style="color: #ffff00;">██</div><div style="color: #0000ff;">██</div><div style="color: #ff00ff;">██</div><div style="color: #00ffff;">██</div><div style="color: #ffffff;">██</div><div style="color: #808080;">██</div>
      `, 'html');
    }
  },

  uname: {
    name: 'uname',
    description: 'Display system information',
    execute: (args: string[]) => {
      if (args.includes('-a')) {
        return createOutput('Portfolio Terminal 1.0.0 React TypeScript x86_64', 'text');
      }
      return createOutput('Portfolio Terminal', 'text');
    }
  },

  fortune: {
    name: 'fortune',
    description: 'Display a random quote',
    execute: () => {
      const quotes = [
        "Code is like humor. When you have to explain it, it's bad. – Cory House",
        "First, solve the problem. Then, write the code. – John Johnson",
        "Experience is the name everyone gives to their mistakes. – Oscar Wilde",
        "In order to be irreplaceable, one must always be different. – Coco Chanel",
        "Java is to JavaScript what car is to Carpet. – Chris Heilmann",
        "Knowledge is power. – Francis Bacon",
        "Sometimes it pays to stay in bed on Monday, rather than spending the rest of the week debugging Monday's code. – Dan Salomon",
        "Perfection is achieved not when there is nothing more to add, but rather when there is nothing more to take away. – Antoine de Saint-Exupery"
      ];
      const randomQuote = quotes[Math.floor(Math.random() * quotes.length)];
      return createOutput(randomQuote, 'text');
    }
  },

  matrix: {
    name: 'matrix',
    description: 'Enter the matrix (change to matrix theme)',
    execute: () => {
      return createOutput('CHANGE_THEME:matrix', 'success');
    }
  },

  hack: {
    name: 'hack',
    description: 'Initiate hacking sequence (just for fun)',
    execute: () => {
      return createOutput(`
<div style="color: #00ff00;">Initiating hack sequence...</div>
<div style="color: #ffff00;">Scanning network... [████████████████████] 100%</div>
<div style="color: #00ff00;">Access granted to portfolio database</div>
<div style="color: #ffffff;">Downloading skills.json... Complete</div>
<div style="color: #ffffff;">Downloading projects.json... Complete</div>
<div style="color: #ffffff;">Downloading experience.json... Complete</div>
<div style="color: #00ffff;">Hack complete! You now have access to all portfolio data.</div>
<div style="color: #ff00ff;">Use 'projects', 'skills', 'experience' commands to view data.</div>
      `, 'html');
    }
  },

  sudo: {
    name: 'sudo',
    description: 'Execute commands with elevated privileges',
    usage: 'sudo [command]',
    execute: (args: string[]) => {
      const command = args.join(' ');
      if (!command) {
        return createOutput('Usage: sudo [command]', 'error');
      }

      if (command === 'rm -rf /') {
        return createOutput(`
<div style="color: #ff0000;">ERROR: Permission denied!</div>
<div style="color: #ffff00;">Nice try, but I'm not letting you delete my portfolio! 😄</div>
<div style="color: #00ffff;">This is a safe environment - no actual system commands are executed.</div>
        `, 'html');
      }

      return createOutput(`sudo: ${command}: command not found\nThis is a portfolio terminal, not a real system! 😉`, 'error');
    }
  },

  ping: {
    name: 'ping',
    description: 'Test network connectivity',
    usage: 'ping [host]',
    execute: (args: string[]) => {
      const host = args[0] || 'portfolio.local';
      return createOutput(`
PING ${host} (127.0.0.1): 56 data bytes
64 bytes from 127.0.0.1: icmp_seq=0 ttl=64 time=0.123ms
64 bytes from 127.0.0.1: icmp_seq=1 ttl=64 time=0.089ms
64 bytes from 127.0.0.1: icmp_seq=2 ttl=64 time=0.095ms

--- ${host} ping statistics ---
3 packets transmitted, 3 packets received, 0.0% packet loss
round-trip min/avg/max/stddev = 0.089/0.102/0.123/0.015 ms
      `, 'text');
    }
  },

  curl: {
    name: 'curl',
    description: 'Transfer data from servers',
    usage: 'curl [url]',
    execute: (args: string[]) => {
      const url = args[0];
      if (!url) {
        return createOutput('Usage: curl [url]', 'error');
      }

      if (url.includes('github.com')) {
        return createOutput(`Connecting to ${url}...\nHTTP/1.1 200 OK\nContent-Type: text/html\n\n<html>GitHub Profile Found!</html>`, 'text');
      }

      return createOutput(`curl: (6) Could not resolve host: ${url}`, 'error');
    }
  },

  top: {
    name: 'top',
    description: 'Display running processes',
    execute: () => {
      return createOutput(`
<div style="color: #00ff00;">PID    COMMAND          %CPU    %MEM</div>
<div style="color: #ffffff;">1      portfolio-term   45.2    12.3</div>
<div style="color: #ffffff;">2      react-renderer   23.1    8.7</div>
<div style="color: #ffffff;">3      three-js-engine  18.9    15.2</div>
<div style="color: #ffffff;">4      terminal-ui      12.8    6.1</div>
<div style="color: #ffffff;">5      physics-sim      8.3     4.2</div>
<div style="color: #ffffff;">6      animation-loop   5.7     2.8</div>
<div style="color: #00ffff;">Total processes: 6</div>
      `, 'html');
    }
  },

  ps: {
    name: 'ps',
    description: 'Display running processes',
    execute: () => {
      return createOutput(`
<div style="color: #00ff00;">  PID TTY          TIME CMD</div>
<div style="color: #ffffff;">    1 pts/0    00:00:01 portfolio</div>
<div style="color: #ffffff;">    2 pts/0    00:00:00 terminal</div>
<div style="color: #ffffff;">    3 pts/0    00:00:00 three-js</div>
<div style="color: #ffffff;">    4 pts/0    00:00:00 physics</div>
      `, 'html');
    }
  },

  ascii: {
    name: 'ascii',
    description: 'Display ASCII art',
    execute: () => {
      return createOutput(`
<div style="color: #00ffff; font-family: monospace; line-height: 1.2;">
    ____             __  ____      ___
   / __ \\____  _____/ /_/ __/___  / (_)___
  / /_/ / __ \\/ ___/ __/ /_/ __ \\/ / / __ \\
 / ____/ /_/ / /  / /_/ __/ /_/ / / / /_/ /
/_/    \\____/_/   \\__/_/  \\____/_/_/\\____/

</div>
<div style="color: #ffff00;">Welcome to my interactive terminal portfolio!</div>
<div style="color: #ffffff;">Built with React, TypeScript, and Three.js</div>
      `, 'html');
    }
  },

  weather: {
    name: 'weather',
    description: 'Check the weather (simulated)',
    execute: () => {
      const conditions = ['Sunny', 'Cloudy', 'Rainy', 'Coding Weather ☀️'];
      const temps = [72, 68, 75, 70, 73];
      const condition = conditions[Math.floor(Math.random() * conditions.length)];
      const temp = temps[Math.floor(Math.random() * temps.length)];

      return createOutput(`
<div style="color: #00ffff;">Weather Report for ${personalInfo.location}</div>
<div style="color: #ffffff;">Condition: ${condition}</div>
<div style="color: #ffffff;">Temperature: ${temp}°F</div>
<div style="color: #ffff00;">Perfect weather for coding! 💻</div>
      `, 'html');
    }
  },

  joke: {
    name: 'joke',
    description: 'Tell a programming joke',
    execute: () => {
      const jokes = [
        "Why do programmers prefer dark mode? Because light attracts bugs! 🐛",
        "How many programmers does it take to change a light bulb? None, that's a hardware problem! 💡",
        "Why do Java developers wear glasses? Because they can't C# 👓",
        "What's a programmer's favorite hangout place? Foo Bar! 🍺",
        "Why did the programmer quit his job? He didn't get arrays! 📊",
        "What do you call a programmer from Finland? Nerdic! 🇫🇮",
        "Why do programmers hate nature? It has too many bugs! 🌿🐛"
      ];
      const joke = jokes[Math.floor(Math.random() * jokes.length)];
      return createOutput(joke, 'text');
    }
  },

  coffee: {
    name: 'coffee',
    description: 'Brew some virtual coffee',
    execute: () => {
      return createOutput(`
<div style="color: #8B4513;">      (  )   (   )  )</div>
<div style="color: #8B4513;">       ) (   )  (  (</div>
<div style="color: #8B4513;">       ( )  (    ) )</div>
<div style="color: #8B4513;">       _____________</div>
<div style="color: #8B4513;">      <_____________> ___</div>
<div style="color: #8B4513;">      |             |/ _ \\</div>
<div style="color: #8B4513;">      |               | | |</div>
<div style="color: #8B4513;">      |               |_| |</div>
<div style="color: #8B4513;">   ___|_______________|\\___/</div>
<div style="color: #8B4513;">  /    \\___________/    \\</div>
<div style="color: #8B4513;"> /                      \\</div>
<div style="color: #8B4513;">/_________________________\\</div>

<div style="color: #ffff00;">☕ Virtual coffee brewing...</div>
<div style="color: #ffffff;">Perfect fuel for coding sessions!</div>
      `, 'html');
    }
  },

  skills2: {
    name: 'skills2',
    description: 'Display skills with progress bars',
    aliases: ['skillbars'],
    execute: () => {
      const skillLevels = {
        'JavaScript': 90,
        'TypeScript': 85,
        'React': 88,
        'Node.js': 82,
        'Python': 75,
        'Three.js': 70
      };

      let output = '<div style="color: #00ff00; font-weight: bold;">🛠️ SKILL LEVELS</div>\n\n';

      Object.entries(skillLevels).forEach(([skill, level]) => {
        const filled = Math.floor(level / 5);
        const empty = 20 - filled;
        const bar = '█'.repeat(filled) + '░'.repeat(empty);

        output += `<div style="color: #ffff00;">${skill.padEnd(12)}</div>`;
        output += `<div style="color: #00ffff;">[${bar}] ${level}%</div>\n`;
      });

      return createOutput(output, 'html');
    }
  },

  music: {
    name: 'music',
    description: 'Play some coding music (ASCII style)',
    execute: () => {
      return createOutput(`
<div style="color: #ff00ff;">♪ ♫ ♪ ♫ Now Playing ♫ ♪ ♫ ♪</div>
<div style="color: #ffffff;">🎵 Lofi Hip Hop - Coding Beats 🎵</div>
<div style="color: #00ffff;">Perfect background music for programming!</div>

<div style="color: #ffff00;">♪ ♫ ♪ ♫ ♪ ♫ ♪ ♫ ♪ ♫ ♪ ♫</div>
<div style="color: #00ff00;">♫ ♪ ♫ ♪ ♫ ♪ ♫ ♪ ♫ ♪ ♫ ♪</div>
<div style="color: #ff00ff;">♪ ♫ ♪ ♫ ♪ ♫ ♪ ♫ ♪ ♫ ♪ ♫</div>

<div style="color: #ffffff;">Use 'stop' to stop the music</div>
      `, 'html');
    }
  },

  stop: {
    name: 'stop',
    description: 'Stop the music',
    execute: () => {
      return createOutput('🔇 Music stopped. Back to coding in silence!', 'text');
    }
  }
};
