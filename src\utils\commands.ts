import { Command, TerminalOutput } from '../types/terminal';
import { personalInfo, skills, projects, experience, education, contactInfo } from '../data/portfolio';

const createOutput = (content: string, type: 'text' | 'html' | 'error' | 'success' | 'warning' = 'text'): TerminalOutput => ({
  type,
  content
});

const formatProjects = (): string => {
  const featuredProjects = projects.filter(p => p.featured);
  const otherProjects = projects.filter(p => !p.featured);
  
  let output = '<div style="color: #00ff00; font-weight: bold;">📁 FEATURED PROJECTS</div>\n\n';
  
  featuredProjects.forEach((project, index) => {
    output += `<div style="color: #ffff00;">${index + 1}. ${project.name}</div>`;
    output += `<div style="color: #ffffff; margin-left: 20px;">${project.description}</div>`;
    output += `<div style="color: #00ffff; margin-left: 20px;">Tech: ${project.technologies.join(', ')}</div>`;
    if (project.githubUrl) output += `<div style="color: #ff00ff; margin-left: 20px;">GitHub: ${project.githubUrl}</div>`;
    if (project.liveUrl) output += `<div style="color: #ff00ff; margin-left: 20px;">Live: ${project.liveUrl}</div>`;
    output += '\n';
  });

  if (otherProjects.length > 0) {
    output += '<div style="color: #00ff00; font-weight: bold;">📂 OTHER PROJECTS</div>\n\n';
    otherProjects.forEach((project, index) => {
      output += `<div style="color: #ffff00;">${index + 1}. ${project.name}</div>`;
      output += `<div style="color: #ffffff; margin-left: 20px;">${project.description}</div>`;
      output += `<div style="color: #00ffff; margin-left: 20px;">Tech: ${project.technologies.join(', ')}</div>`;
      if (project.githubUrl) output += `<div style="color: #ff00ff; margin-left: 20px;">GitHub: ${project.githubUrl}</div>`;
      output += '\n';
    });
  }

  return output;
};

const formatSkills = (): string => {
  let output = '<div style="color: #00ff00; font-weight: bold;">🛠️ TECHNICAL SKILLS</div>\n\n';
  
  Object.entries(skills).forEach(([category, skillList]) => {
    const categoryName = category.charAt(0).toUpperCase() + category.slice(1);
    output += `<div style="color: #ffff00;">${categoryName}:</div>`;
    output += `<div style="color: #ffffff; margin-left: 20px;">${skillList.join(' • ')}</div>\n`;
  });

  return output;
};

const formatExperience = (): string => {
  let output = '<div style="color: #00ff00; font-weight: bold;">💼 WORK EXPERIENCE</div>\n\n';
  
  experience.forEach((exp, index) => {
    output += `<div style="color: #ffff00;">${index + 1}. ${exp.position} at ${exp.company}</div>`;
    output += `<div style="color: #00ffff; margin-left: 20px;">Duration: ${exp.duration}</div>`;
    output += `<div style="color: #ffffff; margin-left: 20px;">${exp.description}</div>`;
    output += `<div style="color: #ff00ff; margin-left: 20px;">Technologies: ${exp.technologies.join(', ')}</div>\n`;
  });

  return output;
};

const formatEducation = (): string => {
  let output = '<div style="color: #00ff00; font-weight: bold;">🎓 EDUCATION</div>\n\n';
  
  education.forEach((edu, index) => {
    output += `<div style="color: #ffff00;">${index + 1}. ${edu.degree}</div>`;
    output += `<div style="color: #00ffff; margin-left: 20px;">Institution: ${edu.institution}</div>`;
    output += `<div style="color: #ffffff; margin-left: 20px;">Duration: ${edu.duration}</div>`;
    if (edu.description) output += `<div style="color: #ffffff; margin-left: 20px;">${edu.description}</div>`;
    output += '\n';
  });

  return output;
};

const formatContact = (): string => {
  let output = '<div style="color: #00ff00; font-weight: bold;">📞 CONTACT INFORMATION</div>\n\n';
  
  output += `<div style="color: #ffff00;">Email:</div>`;
  output += `<div style="color: #ffffff; margin-left: 20px;">${contactInfo.email}</div>\n`;
  
  if (contactInfo.phone) {
    output += `<div style="color: #ffff00;">Phone:</div>`;
    output += `<div style="color: #ffffff; margin-left: 20px;">${contactInfo.phone}</div>\n`;
  }
  
  output += `<div style="color: #ffff00;">Location:</div>`;
  output += `<div style="color: #ffffff; margin-left: 20px;">${contactInfo.location}</div>\n`;
  
  output += `<div style="color: #ffff00;">LinkedIn:</div>`;
  output += `<div style="color: #ff00ff; margin-left: 20px;">${contactInfo.linkedin}</div>\n`;
  
  output += `<div style="color: #ffff00;">GitHub:</div>`;
  output += `<div style="color: #ff00ff; margin-left: 20px;">${contactInfo.github}</div>\n`;
  
  if (contactInfo.website) {
    output += `<div style="color: #ffff00;">Website:</div>`;
    output += `<div style="color: #ff00ff; margin-left: 20px;">${contactInfo.website}</div>\n`;
  }

  return output;
};

export const commands: Record<string, Command> = {
  help: {
    name: 'help',
    description: 'Show all available commands',
    execute: () => {
      const commandList = Object.values(commands).map(cmd => 
        `<div style="color: #00ffff;">${cmd.name.padEnd(12)}</div><div style="color: #ffffff;">${cmd.description}</div>`
      ).join('\n');
      
      return createOutput(`
<div style="color: #00ff00; font-weight: bold;">🚀 AVAILABLE COMMANDS</div>

${commandList}

<div style="color: #ffff00; margin-top: 20px;">💡 Tips:</div>
<div style="color: #ffffff;">• Use TAB for autocompletion</div>
<div style="color: #ffffff;">• Use ↑/↓ arrows for command history</div>
<div style="color: #ffffff;">• Type 'clear' to clear the terminal</div>
      `, 'html');
    }
  },

  about: {
    name: 'about',
    description: 'Learn more about me',
    execute: () => {
      return createOutput(`
<div style="color: #00ff00; font-weight: bold;">👨‍💻 ABOUT ME</div>

<div style="color: #ffff00;">Name:</div>
<div style="color: #ffffff; margin-left: 20px;">${personalInfo.name}</div>

<div style="color: #ffff00;">Title:</div>
<div style="color: #ffffff; margin-left: 20px;">${personalInfo.title}</div>

<div style="color: #ffff00;">Experience:</div>
<div style="color: #ffffff; margin-left: 20px;">${personalInfo.yearsOfExperience}+ years</div>

<div style="color: #ffff00;">Location:</div>
<div style="color: #ffffff; margin-left: 20px;">${personalInfo.location}</div>

<div style="color: #ffff00;">Bio:</div>
<div style="color: #ffffff; margin-left: 20px;">${personalInfo.bio}</div>
      `, 'html');
    }
  },

  projects: {
    name: 'projects',
    description: 'View my portfolio projects',
    execute: () => createOutput(formatProjects(), 'html')
  },

  skills: {
    name: 'skills',
    description: 'Display my technical skills',
    aliases: ['tech'],
    execute: () => createOutput(formatSkills(), 'html')
  },

  experience: {
    name: 'experience',
    description: 'Show my work experience',
    aliases: ['work'],
    execute: () => createOutput(formatExperience(), 'html')
  },

  education: {
    name: 'education',
    description: 'Display my educational background',
    execute: () => createOutput(formatEducation(), 'html')
  },

  contact: {
    name: 'contact',
    description: 'Get my contact information',
    execute: () => createOutput(formatContact(), 'html')
  },

  github: {
    name: 'github',
    description: 'Open my GitHub profile',
    execute: () => {
      window.open(contactInfo.github, '_blank');
      return createOutput(`Opening GitHub profile: ${contactInfo.github}`, 'success');
    }
  },

  resume: {
    name: 'resume',
    description: 'Download my resume',
    execute: () => {
      // Create a download link for resume
      const link = document.createElement('a');
      link.href = '/resume.pdf';
      link.download = 'resume.pdf';
      link.click();
      return createOutput('Downloading resume...', 'success');
    }
  },

  clear: {
    name: 'clear',
    description: 'Clear the terminal screen',
    execute: () => createOutput('CLEAR_TERMINAL', 'text')
  },

  echo: {
    name: 'echo',
    description: 'Display a line of text',
    usage: 'echo [text]',
    execute: (args: string[]) => {
      const text = args.join(' ');
      return createOutput(text || '', 'text');
    }
  },

  ls: {
    name: 'ls',
    description: 'List directory contents',
    execute: () => {
      return createOutput(`
<div style="color: #00ffff;">total 8</div>
<div style="color: #ffff00;">drwxr-xr-x  2 <USER> <GROUP> 4096 ${new Date().toLocaleDateString()} about/</div>
<div style="color: #ffff00;">drwxr-xr-x  2 <USER> <GROUP> 4096 ${new Date().toLocaleDateString()} projects/</div>
<div style="color: #ffff00;">drwxr-xr-x  2 <USER> <GROUP> 4096 ${new Date().toLocaleDateString()} skills/</div>
<div style="color: #ffff00;">drwxr-xr-x  2 <USER> <GROUP> 4096 ${new Date().toLocaleDateString()} experience/</div>
<div style="color: #ffffff;">-rw-r--r--  1 <USER> <GROUP> 2048 ${new Date().toLocaleDateString()} resume.pdf</div>
<div style="color: #ffffff;">-rw-r--r--  1 <USER> <GROUP> 1024 ${new Date().toLocaleDateString()} contact.txt</div>
      `, 'html');
    }
  },

  pwd: {
    name: 'pwd',
    description: 'Print working directory',
    execute: () => createOutput('/home/<USER>', 'text')
  },

  whoami: {
    name: 'whoami',
    description: 'Display current user',
    execute: () => createOutput(personalInfo.name.toLowerCase().replace(' ', ''), 'text')
  },

  date: {
    name: 'date',
    description: 'Display current date and time',
    execute: () => createOutput(new Date().toString(), 'text')
  },

  themes: {
    name: 'themes',
    description: 'List available terminal themes',
    execute: () => {
      return createOutput(`
<div style="color: #00ff00; font-weight: bold;">🎨 AVAILABLE THEMES</div>

<div style="color: #ffff00;">dark</div><div style="color: #ffffff; margin-left: 20px;">Default dark theme</div>
<div style="color: #ffff00;">matrix</div><div style="color: #ffffff; margin-left: 20px;">Green matrix style</div>
<div style="color: #ffff00;">cyberpunk</div><div style="color: #ffffff; margin-left: 20px;">Neon cyberpunk theme</div>
<div style="color: #ffff00;">ubuntu</div><div style="color: #ffffff; margin-left: 20px;">Ubuntu terminal theme</div>
<div style="color: #ffff00;">dracula</div><div style="color: #ffffff; margin-left: 20px;">Dracula color scheme</div>

<div style="color: #00ffff; margin-top: 20px;">Usage: theme [theme-name]</div>
      `, 'html');
    }
  },

  theme: {
    name: 'theme',
    description: 'Change terminal theme',
    usage: 'theme [theme-name]',
    execute: (args: string[]) => {
      const themeName = args[0];
      if (!themeName) {
        return createOutput('Usage: theme [theme-name]\nType "themes" to see available themes.', 'error');
      }

      const availableThemes = ['dark', 'matrix', 'cyberpunk', 'ubuntu', 'dracula'];
      if (!availableThemes.includes(themeName)) {
        return createOutput(`Theme "${themeName}" not found. Type "themes" to see available themes.`, 'error');
      }

      return createOutput(`CHANGE_THEME:${themeName}`, 'success');
    }
  },

  history: {
    name: 'history',
    description: 'Show command history',
    execute: () => createOutput('SHOW_HISTORY', 'text')
  },

  cat: {
    name: 'cat',
    description: 'Display file contents',
    usage: 'cat [filename]',
    execute: (args: string[]) => {
      const filename = args[0];
      if (!filename) {
        return createOutput('Usage: cat [filename]\nAvailable files: about.txt, contact.txt, skills.txt', 'error');
      }

      switch (filename) {
        case 'about.txt':
          return createOutput(`${personalInfo.name}\n${personalInfo.title}\n\n${personalInfo.bio}`, 'text');
        case 'contact.txt':
          return createOutput(`Email: ${contactInfo.email}\nGitHub: ${contactInfo.github}\nLinkedIn: ${contactInfo.linkedin}`, 'text');
        case 'skills.txt':
          return createOutput(`Languages: ${skills.languages.join(', ')}\nFrontend: ${skills.frontend.join(', ')}\nBackend: ${skills.backend.join(', ')}`, 'text');
        default:
          return createOutput(`cat: ${filename}: No such file or directory`, 'error');
      }
    }
  },

  tree: {
    name: 'tree',
    description: 'Display directory tree structure',
    execute: () => {
      return createOutput(`
<div style="color: #00ffff;">portfolio/</div>
<div style="color: #ffffff;">├── about/</div>
<div style="color: #ffffff;">│   ├── bio.txt</div>
<div style="color: #ffffff;">│   └── experience.txt</div>
<div style="color: #ffffff;">├── projects/</div>
<div style="color: #ffffff;">│   ├── featured/</div>
<div style="color: #ffffff;">│   └── archive/</div>
<div style="color: #ffffff;">├── skills/</div>
<div style="color: #ffffff;">│   ├── languages.txt</div>
<div style="color: #ffffff;">│   ├── frameworks.txt</div>
<div style="color: #ffffff;">│   └── tools.txt</div>
<div style="color: #ffffff;">├── contact.txt</div>
<div style="color: #ffffff;">└── resume.pdf</div>
      `, 'html');
    }
  },

  neofetch: {
    name: 'neofetch',
    description: 'Display system information',
    execute: () => {
      return createOutput(`
<div style="color: #00ff00; font-weight: bold;">                    ${personalInfo.name}</div>
<div style="color: #00ff00; font-weight: bold;">                    ${'─'.repeat(personalInfo.name.length)}</div>
<div style="color: #ffff00;">OS:</div><div style="color: #ffffff; margin-left: 20px;">Portfolio Terminal v1.0</div>
<div style="color: #ffff00;">Host:</div><div style="color: #ffffff; margin-left: 20px;">${personalInfo.title}</div>
<div style="color: #ffff00;">Kernel:</div><div style="color: #ffffff; margin-left: 20px;">React ${React.version || '18.x'}</div>
<div style="color: #ffff00;">Uptime:</div><div style="color: #ffffff; margin-left: 20px;">${personalInfo.yearsOfExperience}+ years experience</div>
<div style="color: #ffff00;">Shell:</div><div style="color: #ffffff; margin-left: 20px;">portfolio-terminal</div>
<div style="color: #ffff00;">Terminal:</div><div style="color: #ffffff; margin-left: 20px;">Interactive Portfolio</div>
<div style="color: #ffff00;">CPU:</div><div style="color: #ffffff; margin-left: 20px;">Problem Solving Unit</div>
<div style="color: #ffff00;">Memory:</div><div style="color: #ffffff; margin-left: 20px;">Unlimited creativity</div>
<div style="color: #ffff00;">Location:</div><div style="color: #ffffff; margin-left: 20px;">${personalInfo.location}</div>

<div style="color: #ff0000;">██</div><div style="color: #00ff00;">██</div><div style="color: #ffff00;">██</div><div style="color: #0000ff;">██</div><div style="color: #ff00ff;">██</div><div style="color: #00ffff;">██</div><div style="color: #ffffff;">██</div><div style="color: #808080;">██</div>
      `, 'html');
    }
  },

  uname: {
    name: 'uname',
    description: 'Display system information',
    execute: (args: string[]) => {
      if (args.includes('-a')) {
        return createOutput('Portfolio Terminal 1.0.0 React TypeScript x86_64', 'text');
      }
      return createOutput('Portfolio Terminal', 'text');
    }
  },

  fortune: {
    name: 'fortune',
    description: 'Display a random quote',
    execute: () => {
      const quotes = [
        "Code is like humor. When you have to explain it, it's bad. – Cory House",
        "First, solve the problem. Then, write the code. – John Johnson",
        "Experience is the name everyone gives to their mistakes. – Oscar Wilde",
        "In order to be irreplaceable, one must always be different. – Coco Chanel",
        "Java is to JavaScript what car is to Carpet. – Chris Heilmann",
        "Knowledge is power. – Francis Bacon",
        "Sometimes it pays to stay in bed on Monday, rather than spending the rest of the week debugging Monday's code. – Dan Salomon",
        "Perfection is achieved not when there is nothing more to add, but rather when there is nothing more to take away. – Antoine de Saint-Exupery"
      ];
      const randomQuote = quotes[Math.floor(Math.random() * quotes.length)];
      return createOutput(randomQuote, 'text');
    }
  }
};
