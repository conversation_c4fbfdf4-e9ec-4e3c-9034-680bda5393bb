import React from 'react';
import { GlobalStyles, Container, LeftPanel, RightPanel } from './styles/GlobalStyles';
import { ProfileWithFallback } from './components/Profile/ProfileWithFallback';
import { Terminal } from './components/Terminal/Terminal';

const App: React.FC = () => {
  return (
    <>
      <GlobalStyles />
      <Container>
        <LeftPanel>
          <ProfileWithFallback />
        </LeftPanel>
        <RightPanel>
          <Terminal />
        </RightPanel>
      </Container>
    </>
  );
};

export default App;
